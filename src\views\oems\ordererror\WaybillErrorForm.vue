<template>
  <ContentWrap>
    <el-table  :data="list" :stripe="true" :show-overflow-tooltip="true"
              style="width: 100%;"
          :header-cell-style="{ color: '#606266', textAlign: 'center', fontWeight: 400 }"
          :cell-style="{ textAlign: 'center' }">
    <el-table-column label="编号"  prop="id" />
    <el-table-column label="异常状态" align="center" prop="errorStatus" min-width="90px">
      <template #default="scope">
        <dict-tag :type="DICT_TYPE.OEMS_ERROR_STATUS" :value="scope.row.errorStatus" />
      </template>
    </el-table-column>
    <el-table-column label="车牌号" align="center" prop="vehicleLicenseNumber" min-width="90px"/>
    <el-table-column
      label="上报时间"
      align="center"
      prop="reportTime"
      :formatter="dateFormatter"
      width="180px"
    />
    <el-table-column label="上报站点" align="center" prop="reportBranchName" min-width="90px" />
    <el-table-column label="上报类型" align="center" prop="reportType">
      <template #default="scope">
        <dict-tag :type="DICT_TYPE.OEMS_CREATE_TYPE" :value="scope.row.reportType" />
      </template>
    </el-table-column>
    <el-table-column
      label="回复时间"
      align="center"
      prop="replyTime"
      :formatter="dateFormatter"
      width="180px"
    />
    <el-table-column label="回复站点" align="center" prop="replyBranchName" min-width="90px"/>
    <el-table-column label="是否需要回复" align="center" prop="needReply" min-width="120px">
      <template #default="scope">
        <dict-tag :type="DICT_TYPE.TINYINT_BOOLEAN" :value="scope.row.needReply" />
      </template>
    </el-table-column>
    <el-table-column label="异常件数" align="center" prop="errorNum" />
    <el-table-column label="异常类型" align="center" prop="errorType">
      <template #default="scope">
        <dict-tag :type="DICT_TYPE.OEMS_ERROR_TYPE" :value="scope.row.errorType" />
      </template>
    </el-table-column>
    <el-table-column label="取消原因" align="center" prop="cancelReason" />
    <el-table-column
      label="异常结束时间"
      align="center"
      prop="finishTime"
      :formatter="dateFormatter"
      width="180px"
    />
    <el-table-column label="异常结束操作人员" align="center" prop="finishName" min-width="140"/>
    <el-table-column label="备注" align="center" prop="remark" />
    <el-table-column label="操作" align="center" width="80" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleViewDetail(scope.row.id)"
            v-hasPermi="['oems:order-error:query']"
          >
            详情
          </el-button>
        </template>
    </el-table-column>
  </el-table>
  </ContentWrap>
</template>

<script setup lang="ts">
import { defineProps, watch, ref, onMounted } from 'vue'
import { OrderErrorApi, OrderErrorVO } from '@/api/oems/ordererror'
import { dateFormatter } from '@/utils/formatTime'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { useRouter } from 'vue-router'
const props = defineProps<{
  waybillNo: string
}>()
const router = useRouter()
const loading = ref(true)
const list = ref<OrderErrorVO[]>([])

const queryParams = ref({
  pageNo: 1,
  pageSize: 100, // 固定大一点
  waybillNo: props.waybillNo
})

const getList = async () => {
  loading.value = true
  try {
    queryParams.value.waybillNo = props.waybillNo
    const data = await OrderErrorApi.getOrderErrorPage(queryParams.value)
    list.value = data.list
  } finally {
    loading.value = false
  }
}

/** 查看异常详情 */
const handleViewDetail = (id: number) => {
  router.push({ path: '/oems/ordererror/detail', query: { id: id } })
}


// 监听运单号变化自动刷新
watch(() => props.waybillNo, () => {
  getList()
})

// 初始化
onMounted(() => {
  getList()
})
</script>
