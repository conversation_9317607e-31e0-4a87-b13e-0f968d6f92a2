<template>
  <!-- 顶部栏，紧贴卡片 -->
  <div class="header-bar">
    <div class="order-no">
      <span>异常编号：{{ formData.id }}</span>
      <el-icon style="margin-left: 8px;color:#606266;cursor:pointer" @click="copyErrorId"><CopyDocument /></el-icon>
    </div>
    <div class="title">运 输 异 常 详 情</div>
    <div class="time">上报时间：{{ formatDate(formData.reportTime) }}</div>
  </div>
  <el-card style="border: none;box-shadow: none">
    <!-- tab栏 -->
    <div class="tab-bar">
      <div class="tab-list">
        <div
          v-for="(tab, i) in tabList"
          :key="tab.text" style="cursor: pointer"
          :class="['tab', {active: i === activeTabIndex}]"
          @click="activeTabIndex = i"
          v-show="shouldShowTab(i)"
        >
          {{tab.text}}
        </div>
      </div>
      <div class="custom-steps-bar">
        <div class="custom-steps-flex">
          <template v-for="(step, i) in stepList" :key="step.text">
            <div class="custom-step-flex">
              <div :class="['custom-step-label', { active: step.done }]">{{ step.text }}</div>
              <el-icon :class="['custom-step-icon', {active: step.done}]">
                <CircleCheck v-if="step.done" />
              </el-icon>
            </div>
            <div
              v-if="i < stepList.length - 1"
             :class="['custom-step-line-flex', { active: stepList[i + 1].done }]"
            ></div>
          </template>
        </div>
      </div>
    </div>
    
    <div v-if="activeTabIndex === 0">
      <!-- 基础信息 -->
      <el-form class="block-form" label-width="100px" label-position="right" inline label-suffix=":">
        <div class="section-title-row">
          <span class="section-title">异常基础信息</span>
          <span class="section-right">
            <dict-tag :type="DICT_TYPE.OEMS_ERROR_STATUS" :value="formData.errorStatus" />
          </span>
        </div>
        <el-form-item label="运单编号">
          <el-link @click="handleWaybillNoClick(formData.waybillNo)" class="form-value">
            {{ formData.waybillNo }}
          </el-link>
        </el-form-item>
        <el-form-item label="车牌号"><span class="form-value">{{ formData.vehicleLicenseNumber }}</span></el-form-item>
        <el-form-item label="异常类型">
          <dict-tag :type="DICT_TYPE.OEMS_ERROR_TYPE" :value="formData.errorType" />
        </el-form-item>
        <el-form-item label="异常件数"><span class="form-value">{{ formData.errorNum }}</span></el-form-item>
        <el-form-item label="上报类型">
          <dict-tag :type="DICT_TYPE.OEMS_CREATE_TYPE" :value="formData.reportType" />
        </el-form-item>
        <el-form-item label="是否需要回复">
          <dict-tag :type="DICT_TYPE.TINYINT_BOOLEAN" :value="formData.needReply" />
        </el-form-item>
      </el-form>

      <!-- 上报信息/回复信息 -->
      <div class="form-row" style="align-items: stretch;">
        <el-form class="block-form" label-width="100px" label-position="right" inline style="flex:1;" label-suffix=":">
          <div class="section-title" style="width: 100%; font-size: 15px;">上报信息</div>
          <el-form-item label="上报站点"><span class="form-value">{{ formData.reportBranchName }}</span></el-form-item>
          <el-form-item label="上报时间"><span class="form-value">{{ formatDate(formData.reportTime) }}</span></el-form-item>
          <el-form-item label="备注" style="width: 100%;">
            <span class="form-value">{{ formData.remark || '无' }}</span>
          </el-form-item>
        </el-form>
        <el-form class="block-form" label-width="100px" label-position="right" inline style="flex:1; min-width:260px;" label-suffix=":">
          <div class="section-title" style="width: 100%; font-size: 15px;">回复信息</div>
          <el-form-item label="回复站点"><span class="form-value">{{ formData.replyBranchName || '未回复' }}</span></el-form-item>
          <el-form-item label="回复时间"><span class="form-value">{{ formatDate(formData.replyTime) || '未回复' }}</span></el-form-item>
          <el-form-item label="结束时间" v-if="formData.errorStatus === 3">
            <span class="form-value">{{ formatDate(formData.finishTime) || '未结束' }}</span>
          </el-form-item>
          <el-form-item label="结束操作人" v-if="formData.errorStatus === 3">
            <span class="form-value">{{ formData.finishName || '未结束' }}</span>
          </el-form-item>
          <el-form-item label="取消原因" v-if="formData.errorStatus === 4" style="width: 100%;">
            <span class="form-value">{{ formData.cancelReason || '无' }}</span>
          </el-form-item>
        </el-form>
      </div>

      <!-- 异常图片 -->
      <el-form class="block-form" inline label-suffix=":" v-if="formData.errorImgUrl">
        <div class="section-title" style="width: 100%;">异常图片</div>
        <div class="image-section" style="width: 100%;">
          <el-image
          
            :src="formData.errorImgUrl"
            :preview-src-list="[formData.errorImgUrl]"
            fit="cover"
          />
        </div>
      </el-form>
    </div>

    <!-- 异常回复记录 -->
    <div v-if="activeTabIndex === 1">
      <el-form class="block-form" inline label-suffix=":">
        <div class="section-title" style="width: 100%;">异常回复记录</div>
        <div class="table-section" style="width: 100%;">
          <el-table
            :data="errorRecords"
            border
            style="width: 100%;"
            :header-cell-style="{ color: '#606266', textAlign: 'center', fontWeight: 400 }"
            :cell-style="{ textAlign: 'center' }"
            v-loading="recordsLoading"
          >
            <el-table-column prop="replyBranchName" label="回复站点" width="180"/>
            <el-table-column prop="replyContent" label="回复内容" min-width="200" />
            <el-table-column prop="replyTime" label="回复时间" width="180">
              <template #default="scope">
                {{ formatDate(scope.row.replyTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="replyTime" label="回复图片" min-width="200">
              <template #default="scope">
                <el-image
                  v-if="scope.row.replyImgUrl"
                  style="width: 100px"
                  :src="scope.row.replyImgUrl"
                  :preview-src-list="[scope.row.replyImgUrl]"
                  fit="cover"
                />
                <span v-else style="color: #909399;">无图片</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="button-group-form">
      <div class="button-group-right">
        <el-button @click="goBack" class="action-btn">返回</el-button>
        <el-button 
          v-if="formData.errorStatus === 1 || formData.errorStatus === 2" 
          type="primary" 
          class="action-btn"
          @click="handleCancel"
        >
          取消上报
        </el-button>
        <el-button
          v-if="(formData.errorStatus === 1 || formData.errorStatus === 2) && formData.needReply === 0"
          type="primary"
          class="action-btn"
          @click="handleReply"
        >
          异常回复
        </el-button>
        <el-button 
          v-if="formData.errorStatus === 2" 
          type="primary" 
          class="action-btn"
          @click="handleFinish"
        >
          异常结束
        </el-button>
      </div>
    </div>
  </el-card>

  <!-- 异常回复弹窗 -->
  <OrderErrorRecordsForm ref="recordFormRef" @success="handleReplySuccess" :loginDept="loginDept" />
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CopyDocument, CircleCheck } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/formatTime'
import { OrderErrorApi, OrderErrorVO } from '@/api/oems/ordererror'
import { DICT_TYPE } from '@/utils/dict'
import OrderErrorRecordsForm from './OrderErrorRecordsForm.vue'
import * as DeptApi from '@/api/system/dept'

defineOptions({ name: 'OrderErrorDetail' })

const route = useRoute()
const router = useRouter()

// 响应式数据
const formData = ref<OrderErrorVO>({} as OrderErrorVO)
const errorRecords = ref([])
const loading = ref(false)
const recordsLoading = ref(false)
const activeTabIndex = ref(0)
const loginDept = ref()

// Tab列表
const tabList = [
  { text: '异常详情' },
  { text: '回复记录' }
]

// 控制tab显示的函数
const shouldShowTab = (tabIndex: number) => {
  switch (tabIndex) {
    case 0: // 异常详情
      return true
    case 1: // 回复记录
      return formData.value.errorStatus !== 1 && formData.value.errorStatus !== 4
    default:
      return true
  }
}

// 状态步骤
const stepList = computed(() => {
  const status = formData.value.errorStatus
  const steps = [
    { text: '已上报', done: status >= 1 },
    { text: '已回复', done: status >= 2 },
    { text: '已解决', done: status === 3 }
  ]
  
  // 如果是取消状态，显示不同的步骤
  if (status === 4) {
    return [
      { text: '已上报', done: true },
      { text: '已取消', done: true }
    ]
  }
  
  return steps
})

// 获取异常详情
const getErrorDetail = async () => {
  const id = route.query.id as string
  if (!id) {
    ElMessage.error('缺少异常ID参数')
    return
  }
  
  try {
    loading.value = true
    formData.value = await OrderErrorApi.getOrderError(Number(id))
  } catch (error) {
    console.error('获取异常详情失败:', error)
    ElMessage.error('获取异常详情失败')
  } finally {
    loading.value = false
  }
}

// 获取异常回复记录
const getErrorRecords = async () => {
  const id = route.query.id as string
  if (!id) return
  
  try {
    recordsLoading.value = true
    errorRecords.value = await OrderErrorApi.getErrorRecordsListByOrderErrorId(Number(id))
  } catch (error) {
    console.error('获取回复记录失败:', error)
  } finally {
    recordsLoading.value = false
  }
}

// 复制异常编号
const copyErrorId = () => {
  navigator.clipboard.writeText(formData.value.id?.toString() || '')
  ElMessage.success('异常编号已复制到剪贴板')
}

// 跳转到运单详情
const handleWaybillNoClick = (waybillNo: string) => {
  router.push({ path: '/wbms/waybillDetailNew', query: { waybillNo: waybillNo } })
}

// 返回列表
const goBack = () => {
  router.back()
}

// 异常回复
const recordFormRef = ref()
const handleReply = () => {
  recordFormRef.value?.open(formData.value.id)
}

// 回复成功后的处理
const handleReplySuccess = () => {
  getErrorDetail()
  getErrorRecords()
}

// 异常结束
const handleFinish = async () => {
  try {
    await ElMessageBox.confirm('确认结束该异常吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const updateData = { ...formData.value, errorStatus: 3 }
    await OrderErrorApi.updateOrderError(updateData)
    ElMessage.success('异常已结束')
    await getErrorDetail()
  } catch (error) {
    console.error('结束异常失败:', error)
  }
}

// 取消上报
const handleCancel = async () => {
  try {
    const { value: cancelReason } = await ElMessageBox.prompt('请输入取消原因', '取消上报', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '取消原因不能为空'
    })
    
    const updateData = { 
      ...formData.value, 
      errorStatus: 4,
      cancelReason: cancelReason
    }
    await OrderErrorApi.updateOrderError(updateData)
    ElMessage.success('已取消上报')
    await getErrorDetail()
  } catch (error) {
    console.error('取消上报失败:', error)
  }
}

// 页面初始化
onMounted(async () => {
  loginDept.value = await DeptApi.getLoginUserDept()
  getErrorDetail()
  getErrorRecords()
})
</script>

<style scoped>
.header-bar {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 0;

}
.header-bar .order-no {
  color: #f25643;
  margin-right: 16px;
  font-size: 14px;

}
.header-bar .title {
  font-size: 20px;
  text-align-last: justify;
  color: #303133;
}
.header-bar .time {
  color: #909399;
  font-size: 14px;
}
.form-value {
  color: #303133;
  font-size: 14px;
}
.tab {
  padding: 8px 16px;
  margin-right: 24px;
  color: #606266;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s;
}
.tab.active {
  color: #409eff;
  border-bottom-color: #409eff;
}
.tab-bar {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  background: #fff;
  width: 100%;
}
.tab-list {
  display: flex;
  align-items: center;
  flex: 1;
}
.custom-steps-bar {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
  min-width: 300px;
}
.custom-steps-flex {
  display: flex;
  align-items: center;
}
.custom-step-flex {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
}
.custom-step-label {
  color: #909399;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 4px;
  text-align: center;
  line-height: 1.2;
  padding: 0 8px;
}
.custom-step-label.active {
  color: #13c08a;
}
.custom-step-icon {
  font-size: 16px;
  color: #c0c4cc;
  margin-top: 4px;
}
.custom-step-icon.active {
  color: #13c08a;
}
.custom-step-line-flex {
  width: 50px;
  height: 3px;
  background: #ebeef5;
  margin: 0 4px 20px 0;
  flex: none;
}
.custom-step-line-flex.active {
  background: #13c08a;
    height: 1.5px;
}
.section-title-row {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}
.section-title {
  font-size: 16px;
  margin-bottom: 0;
  font-weight: normal;
}
.section-right {
  color: #909399;
  font-size: 14px;
  margin-left: 16px;
}
.table-section {
  margin: 8px 0 12px 0;
}
.image-section {
  margin: 8px 0 12px 0;
}
.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 0;
  align-items: flex-start;
  flex-wrap: wrap;
}
.block-form {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 14px;
  margin-bottom: 16px;
  background: #fff;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
}
.block-form .el-form-item {
  min-width: 220px;
  margin-bottom: 8px;
}
.block-form .el-form-item:last-of-type {
  margin-bottom: 0 !important;
}
.block-form .section-title,
.block-form .section-title-row {
  margin-bottom: 8px;
}
.button-group-form {
  margin-bottom: 16px;
  background: transparent;
  display: flex;
  justify-content: flex-end;
}
.button-group-right {
  display: flex;
  gap: 12px;
}
.action-btn {
  border-radius: 6px;
  background: #fff;
  border: 1px solid #dcdfe6;
  color: #303133;
  font-weight: 400;
  min-width: 60px;
  padding: 0 18px;
}
.action-btn.el-button--primary {
  background: #409eff;
  color: #fff;
  border: 1px solid #409eff;
}
.action-btn.el-button--success {
  background: #67c23a;
  color: #fff;
  border: 1px solid #67c23a;
}
.action-btn.el-button--danger {
  background: #f56c6c;
  color: #fff;
  border: 1px solid #f56c6c;
}
</style>
