import request from '@/config/axios'

// 运单 VO
export interface WaybillVO {
  id: number // id
  waybillNo: string // 运单号
  orderId: number // 订单id
  orderNo: string // 订单号
  waybillStatus: string // 运单状态
  transportType: number // 运输类型
  shipperId: number // 发货人id
  consigneeId: number // 收货人id
  pickUpType: string // 取件类型
  arriveTime: Date //
  isDelivery: boolean//是否送货
  takeDelivery: boolean
  deliveryType: string // 送货类型
  deliveryTime: Date,
  expenseId: number // 费用表id
  waitReleaseGoods: boolean // 等通知放货
  existReceipt: boolean // 是否要回单
  receiptType: string // 回单类型
  receiveTime: Date, // 揽货时间
  receiptNum: number // 回单份数
  receiptStatus: string // 回单状态
  positionId: number // 位置表id
  shipperPay: number // 寄方付款
  consigneePay: number // 收方付款
  totalCost: number // 总费用
  orderRefNo: string // 关联单号
  existPicture: boolean // 是否有照片
  waybillImgUrl: string
  existRemark: boolean // 是否有备注
  companyRemark: string // 公司备注
  customerRemark: string // 客户备注
  deptId: number // 部门id
  userId: number // 用户id
  isLock: boolean // 是否锁定
  isTransfer: boolean //是否中转
  startDeptId: number
  startDeptName: string
  endDeptId: number
  endDeptName: string
  destination: number
  destinationName: string
  waybillAccidentStatus: string // 意外状态
  goodsList?:[{
    goodsName: string,
    goodsPackageUnit: string,
    goodsNum: number,
    goodsVolume: number,
    goodsWeight: number,
    goodsPrice: number
  }],
  waybillBars?:[{
    id: string,
    waybillId: string,
    waybillNo: string,
    orderBarNo: number,
    currentDeptId: number,
    currentDeptName: number,
    currentDeptType: number,
    orderBarStatus: number
  }],
  shipperName: string,
  shipperMobile: string,
  shipperAreaId: number,
  shipperDetailAddress: string,
  shipperIdCard: string,
  consigneeName: string,
  consigneeMobile:string,
  consigneeAreaId: number,
  consigneeDetailAddress: string,
  biaozhunYf: undefined,
  zhekouYf: undefined,
  zhehouYf: undefined,
  huidanfuYf: undefined,
  koufuYf: undefined,
  tifuYf: undefined,
  tifuyuejieYf: undefined,
  xianfuYf: undefined,
  xianfuyuejieYf: undefined,
  xiayouYf: undefined,
  xiayouYfPt: undefined,
  baoxianfei: undefined,
  baoxianfeiFl: undefined,
  baozhifei: undefined,
  baozhifeiPt: undefined,
  dianfufei: undefined,
  dianfufeiPt: undefined,
  jiehuofei: undefined,
  jiehuofeiPt: undefined,
  songhuofei: undefined,
  songhuofeiPt: undefined,
  zhidanfei: undefined,
  zhidanfeiPt: undefined,
  huidanfei: undefined,
  huidanfeiPt: undefined,
  konghuofei: undefined,
  konghuofeiPt: undefined,
  waizhuanfei: undefined,
  waizhuanfeiPt: undefined,
  peisongfei: undefined,
  yongjin: undefined,
  yongjinPt: undefined,
  yongjinSkr: undefined,
  yongjinAccount: undefined,
  yongjinMobile: undefined,
  other1: undefined,
  other1Pt: undefined,
  other2: undefined,
  other2Pt: undefined,
  other3: undefined,
  other3Pt: undefined,
  other4: undefined,
  other4Pt: undefined,
  other5: undefined,
  other5Pt: undefined,
  other6: undefined,
  other6Pt: undefined,
  other7: undefined,
  other7Pt: undefined,
  other8: undefined,
  other8Pt: undefined,
  other9: undefined,
  other9Pt: undefined,
  collectAgeing: undefined
  collectBankName: string // 代收银行名称
  collectBankAccount: string // 代收银行账户
  collector: string // 代收人
  collectPayments: number // 代收款
  collectHandlingFee: number // 代收手续费
  collectHandlingFl: undefined //代收手续费率
  signTime: Date
}

export interface DelWaybillVO{
  ids: []
}

export interface QueWaybillVO{
  nos: []
}

// 运单 API
export const WaybillApi = {

    // 查询运单分页
  getWaybillNo: async () => {
    return await request.get({ url: `/wbms/waybill/get-waybillNo`})
  },

  // 查询运单分页
  getWaybillPage: async (params: any) => {
    return await request.get({ url: `/wbms/waybill/page`, params })
  },

  // 查询运单详情
  getWaybill: async (id: number) => {
    return await request.get({ url: `/wbms/waybill/get?id=` + id })
  },

  // 查询运单详情
  getWaybillByWaybillNo: async (waybillNo: string) => {
    return await request.get({ url: `/wbms/waybill/get-by-waybillNo?waybillNo=` + waybillNo })
  },

  // 模糊查询运单列表
  getWaybillByWaybillNoLike: async (waybillNo: string) => {
    return await request.get({ url: `/wbms/waybill/get-by-waybillNo-list?waybillNo=` + waybillNo })
  },

  // 查询运单详情
  computeFee: async (id: number) => {
    return await request.get({ url: `/wbms/waybill/compute?id=` + id })
  },

  // 新增运单
  createWaybill: async (data: WaybillVO) => {
    return await request.post({ url: `/wbms/waybill/create`, data })
  },

  // 计算运单费用
  computeFeeHj: async (data: WaybillVO) => {
    return await request.post({ url: `/wbms/waybill/computeFee`, data })
  },

  // 删除运单
  deleteWaybills: async (data: DelWaybillVO) => {
    return await request.post({ url: `/wbms/waybill/deleteSelect`, data })
  },

  // 查询多条运单
  selectWaybills: async (data: QueWaybillVO) => {
    return await request.post({ url: `/wbms/waybill/list`, data })
  },
  

  // 修改运单
  updateWaybill: async (data: WaybillVO) => {
    return await request.put({ url: `/wbms/waybill/update`, data })
  },

  // 删除运单
  deleteWaybill: async (id: number) => {
    return await request.delete({ url: `/wbms/waybill/delete?id=` + id })
  },

  // 导出运单 Excel
  exportWaybill: async (params) => {
    return await request.download({ url: `/wbms/waybill/export-excel`, params })
  },

    // 修改运单
  updateWaybillStatus: async (data: WaybillVO) => {
    return await request.put({ url: `/wbms/waybill/update-waybill`, data })
  },

  // ==================== 子表（运单子单） ====================

  // 获得运单子单列表
  getWaybillBarListByWaybillNo: async (waybillNo) => {
    return await request.get({ url: `/wbms/waybill/waybill-bar/list-by-waybill-no?waybillNo=` + waybillNo })
  },


//   // 获取订单面单数据(模版)
//   getOrderPrintContent: async ( templateKey: string, orderNo : string) => {
// 	return request.get({url: '/oms/order/get-print-order-content',
// 		method: 'GET',
// 		data: { orderNo , templateKey}
// 	})
// }

  getWaybillPrintContent: async ( templateKey: string, waybillNo : string) => {
	  return await request.get({ url: `/wbms/waybill/get-print-content`, params: { waybillNo , templateKey}})
  }

}
