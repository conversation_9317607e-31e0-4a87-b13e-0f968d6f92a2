<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百达TMS - 智慧物流管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 导航栏 */
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: white;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: #ffd700;
        }

        /* 英雄区域 */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 120px 0 80px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="0,100 1000,0 1000,100"/></svg>');
            background-size: cover;
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            animation: fadeInUp 1s ease-out;
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .cta-button {
            display: inline-block;
            background: #ffd700;
            color: #333;
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: bold;
            transition: transform 0.3s, box-shadow 0.3s;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
        }

        /* 特性区域 */
        .features {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #333;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #667eea;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #333;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        /* 模块展示 */
        .modules {
            padding: 80px 0;
            background: white;
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 3rem;
        }

        .module-item {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            transition: transform 0.3s;
        }

        .module-item:hover {
            transform: scale(1.05);
        }

        .module-item h4 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }

        /* 技术栈 */
        .tech-stack {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .tech-item {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        }

        .tech-item img {
            width: 60px;
            height: 60px;
            margin-bottom: 1rem;
        }

        /* 统计数据 */
        .stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            text-align: center;
        }

        .stat-item h3 {
            font-size: 3rem;
            margin-bottom: 0.5rem;
            color: #ffd700;
        }

        .stat-item p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        /* 页脚 */
        .footer {
            background: #333;
            color: white;
            padding: 40px 0;
            text-align: center;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h4 {
            margin-bottom: 1rem;
            color: #ffd700;
        }

        .footer-section p, .footer-section a {
            color: #ccc;
            text-decoration: none;
            line-height: 1.8;
        }

        .footer-section a:hover {
            color: #ffd700;
        }

        /* 动画 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .features-grid,
            .modules-grid,
            .tech-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-content">
                <div class="logo">百达TMS</div>
                <ul class="nav-links">
                    <li><a href="#features">功能特性</a></li>
                    <li><a href="#modules">业务模块</a></li>
                    <li><a href="#tech">技术架构</a></li>
                    <li><a href="#contact">联系我们</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>百达TMS智慧物流管理系统</h1>
                <p>基于Vue3 + TypeScript + Element Plus构建的现代化物流运输管理平台</p>
                <a href="https://baidalt.com.cn" class="cta-button">立即体验</a>
            </div>
        </div>
    </section>

    <!-- 功能特性 -->
    <section id="features" class="features">
        <div class="container">
            <h2 class="section-title">核心特性</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🚛</div>
                    <h3>运单管理</h3>
                    <p>全流程运单管理，支持运单录入、修改、跟踪、签收等完整业务流程，提供实时状态更新和异常处理。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🚗</div>
                    <h3>车辆管理</h3>
                    <p>完善的车辆档案管理，包括车辆信息、证件管理、保险年检、事故记录、加油充电等全方位管理。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">👨‍💼</div>
                    <h3>司机管理</h3>
                    <p>司机信息管理、证件管理、车人绑定关系维护，确保运输安全和合规性。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>异常管理</h3>
                    <p>运输异常处理、无主货管理、弃货申请、退货处理、理赔管理等完整的异常处理流程。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💰</div>
                    <h3>费用管理</h3>
                    <p>运费计算、结算管理、付款管理，支持多种付费方式和结算模式。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔄</div>
                    <h3>流程管理</h3>
                    <p>内置工作流引擎，支持审批流程自定义，提供完整的业务流程管控。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 业务模块 -->
    <section id="modules" class="modules">
        <div class="container">
            <h2 class="section-title">业务模块</h2>
            <div class="modules-grid">
                <div class="module-item">
                    <h4>OMS订单管理</h4>
                    <p>订单录入、修改、跟踪</p>
                </div>
                <div class="module-item">
                    <h4>WBMS运单管理</h4>
                    <p>运单全生命周期管理</p>
                </div>
                <div class="module-item">
                    <h4>TMS运输管理</h4>
                    <p>车次管理、运输调度</p>
                </div>
                <div class="module-item">
                    <h4>VMS车辆管理</h4>
                    <p>车辆档案、司机管理</p>
                </div>
                <div class="module-item">
                    <h4>OEMS异常管理</h4>
                    <p>异常处理、理赔管理</p>
                </div>
                <div class="module-item">
                    <h4>EXP快递管理</h4>
                    <p>快递派送、签收管理</p>
                </div>
                <div class="module-item">
                    <h4>FEE费用管理</h4>
                    <p>费用计算、结算管理</p>
                </div>
                <div class="module-item">
                    <h4>系统管理</h4>
                    <p>用户权限、基础配置</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 技术架构 -->
    <section id="tech" class="tech-stack">
        <div class="container">
            <h2 class="section-title">技术架构</h2>
            <div class="tech-grid">
                <div class="tech-item">
                    <div style="font-size: 3rem; color: #4FC08D;">⚡</div>
                    <h4>Vue 3</h4>
                    <p>现代化前端框架</p>
                </div>
                <div class="tech-item">
                    <div style="font-size: 3rem; color: #3178C6;">📘</div>
                    <h4>TypeScript</h4>
                    <p>类型安全的JavaScript</p>
                </div>
                <div class="tech-item">
                    <div style="font-size: 3rem; color: #409EFF;">🎨</div>
                    <h4>Element Plus</h4>
                    <p>企业级UI组件库</p>
                </div>
                <div class="tech-item">
                    <div style="font-size: 3rem; color: #646CFF;">⚡</div>
                    <h4>Vite</h4>
                    <p>极速构建工具</p>
                </div>
                <div class="tech-item">
                    <div style="font-size: 3rem; color: #6DB33F;">🍃</div>
                    <h4>Spring Boot</h4>
                    <p>企业级后端框架</p>
                </div>
                <div class="tech-item">
                    <div style="font-size: 3rem; color: #336791;">🐘</div>
                    <h4>MySQL</h4>
                    <p>可靠的数据库</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 统计数据 -->
    <section class="stats">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item">
                    <h3>50+</h3>
                    <p>业务功能模块</p>
                </div>
                <div class="stat-item">
                    <h3>100+</h3>
                    <p>API接口</p>
                </div>
                <div class="stat-item">
                    <h3>20+</h3>
                    <p>数据字典类型</p>
                </div>
                <div class="stat-item">
                    <h3>99.9%</h3>
                    <p>系统稳定性</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer id="contact" class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>产品特色</h4>
                    <p>• 模块化设计，易于扩展</p>
                    <p>• 现代化技术栈</p>
                    <p>• 完整的权限管理</p>
                    <p>• 丰富的业务功能</p>
                </div>
                <div class="footer-section">
                    <h4>技术支持</h4>
                    <p>• Vue 3 + TypeScript</p>
                    <p>• Element Plus UI</p>
                    <p>• Spring Boot 后端</p>
                    <p>• 微服务架构支持</p>
                </div>
                <div class="footer-section">
                    <h4>联系方式</h4>
                    <p>📧 邮箱：<EMAIL></p>
                    <p>📞 电话：400-123-4567</p>
                    <p>🌐 官网：www.baidatms.com</p>
                    <p>📍 地址：北京市朝阳区</p>
                </div>
            </div>
            <div style="border-top: 1px solid #555; padding-top: 20px; margin-top: 20px;">
                <p>&copy; 2024 百达TMS智慧物流管理系统. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // 观察所有卡片元素
        document.querySelectorAll('.feature-card, .module-item, .tech-item').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });

        // 数字动画
        function animateNumbers() {
            const stats = document.querySelectorAll('.stat-item h3');
            stats.forEach(stat => {
                const target = parseInt(stat.textContent);
                const suffix = stat.textContent.replace(/[0-9]/g, '');
                let current = 0;
                const increment = target / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    stat.textContent = Math.floor(current) + suffix;
                }, 50);
            });
        }

        // 当统计区域进入视口时开始动画
        const statsSection = document.querySelector('.stats');
        const statsObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateNumbers();
                    statsObserver.unobserve(entry.target);
                }
            });
        });
        statsObserver.observe(statsSection);
    </script>
</body>
</html>
