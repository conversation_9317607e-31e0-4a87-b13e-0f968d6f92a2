<template>
  <Dialog title="异常回复" v-model="dialogVisible" width="600px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="异常编号">
        <el-input v-model="errorId" disabled />
      </el-form-item>
      <el-form-item label="回复内容" prop="replyContent">
        <el-input
          v-model="formData.replyContent"
          type="textarea"
          :rows="4"
          placeholder="请输入回复内容"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="回复站点">
        <el-input v-model="formData.replyBranchName" disabled />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { OrderErrorApi } from '@/api/oems/ordererror'
import { getUserProfile } from '@/api/system/user/profile'

defineOptions({ name: 'OrderErrorReplyForm' })

const emit = defineEmits(['success'])

// 响应式数据
const dialogVisible = ref(false)
const formLoading = ref(false)
const errorId = ref('')
const formRef = ref()

const formData = reactive({
  replyContent: '',
  replyBranchName: '',
  replyBranchId: undefined,
  replyTime: undefined,
  replyUserName: ''
})

const formRules = reactive({
  replyContent: [
    { required: true, message: '请输入回复内容', trigger: 'blur' },
    { min: 1, max: 500, message: '回复内容长度在 1 到 500 个字符', trigger: 'blur' }
  ]
})

// 打开弹窗
const open = async (orderErrorId: string) => {
  dialogVisible.value = true
  errorId.value = orderErrorId
  resetForm()
  
  // 获取当前用户信息
  try {
    const userProfile = await getUserProfile()
    formData.replyBranchName = userProfile.dept?.name || ''
    formData.replyBranchId = userProfile.dept?.id
    formData.replyUserName = userProfile.nickname || ''
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  formData.replyContent = ''
  formData.replyBranchName = ''
  formData.replyBranchId = undefined
  formData.replyTime = undefined
  formData.replyUserName = ''
  formRef.value?.resetFields()
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return

  try {
    formLoading.value = true
    
    // 构造更新数据
    const updateData = {
      id: Number(errorId.value),
      errorStatus: 2, // 设置为已回复状态
      replyBranchId: formData.replyBranchId,
      replyBranchName: formData.replyBranchName,
      replyTime: Date.now(),
      replyUserName: formData.replyUserName
    }
    
    // 更新异常状态
    await OrderErrorApi.updateOrderError(updateData)
    
    // 创建回复记录（如果有相关API）
    // await OrderErrorApi.createErrorRecord({
    //   orderErrorId: Number(errorId.value),
    //   replyContent: formData.replyContent,
    //   replyBranchId: formData.replyBranchId,
    //   replyBranchName: formData.replyBranchName,
    //   replyTime: Date.now(),
    //   replyUserName: formData.replyUserName
    // })
    
    ElMessage.success('回复成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('回复失败:', error)
    ElMessage.error('回复失败')
  } finally {
    formLoading.value = false
  }
}

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped>
.el-form-item {
  margin-bottom: 20px;
}
</style>
