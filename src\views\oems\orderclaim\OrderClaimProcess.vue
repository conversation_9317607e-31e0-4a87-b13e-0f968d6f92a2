<template>
  <div class="page-container">
    <div class="content-wrapper">
      <!-- 顶部信息栏 -->
      <el-row class="top-bar">
        <el-col :span="10">
          <div class="claim-info">
            <span style="font-size: 14px;margin-right: 10px">理赔编号：{{ formData.id }}</span>
            <el-icon @click="copyClaimId"><CopyDocument style="color:#606266;cursor: pointer"/></el-icon>
          </div>
        </el-col>
        <el-col :span="4" style="text-align: center">
          <div class="title">{{ isPaymentMode ? '理 赔 付 款' : '理 赔 处 理' }}</div>
        </el-col>
        <el-col :span="10" style="text-align: right">
          <div style="color: #606266;font-size: 14px">处理时间：{{ formatDate(new Date()) }}</div>
        </el-col>
      </el-row>

      <!-- 沟通记录卡片 -->
      <el-card class="communication-card box-card">
        <template #header>
          <span>沟通记录</span>
        </template>
        <el-form :model="formData" ref="communicationForm">
          <el-table
            :data="communicationRecords"
            border
            style="width: 100%;"
            class="center-cell-table"
          >
            <el-table-column
              prop="replyBranchName"
              label="回复站点名"
              width="150"
            >
              <template #header>
                <span style="color: #606266;font-weight: normal">回复站点名</span>
              </template>
              <template #default="scope">
                <el-form-item>
                  <el-input v-model="scope.row.replyBranchName" size="large" class="transparent-border-input center-input" :disabled="isPaymentMode" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="replyContent"
              label="回复内容"
              min-width="200"
            >
              <template #header>
                <span style="color: #606266;font-weight: normal">回复内容</span>
              </template>
              <template #default="scope">
                <el-form-item>
                  <el-input v-model="scope.row.replyContent" type="textarea" :rows="2" size="large" class="transparent-border-input" :disabled="isPaymentMode" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="replyTime"
              label="回复时间"
              width="210"
            >
              <template #header>
                <span style="color: #606266;font-weight: normal">回复时间</span>
              </template>
              <template #default="scope">
                <el-form-item>
                  <el-date-picker
                    v-model="scope.row.replyTime"
                    type="datetime"
                    value-format="x"
                    size="large"
                    class="transparent-border-input"
                    :disabled="isPaymentMode"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="remark"
              label="备注"
              width="200"
            >
              <template #header>
                <span style="color: #606266;font-weight: normal">备注</span>
              </template>
              <template #default="scope">
                <el-form-item>
                  <el-input v-model="scope.row.remark" size="large" class="transparent-border-input center-input" :disabled="isPaymentMode" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" v-if="!isPaymentMode">
              <template #header>
                <span style="color: #606266;font-weight: normal">操作</span>
              </template>
              <template #default="scope">
                <div style="text-align: center">
                  <!-- 删除按钮：当有多行数据时显示 -->
                  <el-icon size="30px" color="#f25643" tabindex="0"
                           @click="deleteCommunication(scope.$index)" @keydown.enter="deleteCommunication(scope.$index)"
                           v-if="communicationRecords.length > 1"
                           style="cursor: pointer; margin-right: 8px;"><Remove /></el-icon>
                  <!-- 添加按钮：在最后一行显示 -->
                  <el-icon size="30px" color="#1e83e9" tabindex="0"
                           @click="addCommunication()" @keydown.enter="addCommunication()"
                           v-if="scope.$index === communicationRecords.length - 1"
                           style="cursor: pointer;"><CirclePlus /></el-icon>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </el-card>

      <!-- 理赔定责卡片 -->
      <el-card class="liability-card box-card">
        <template #header>
          <span>理赔定责</span>
        </template>
        <el-form :model="formData" ref="liabilityForm">
          <el-table
            :data="liabilityRecords"
            border
            style="width: 100%;"
            class="center-cell-table"
          >
            <el-table-column
              prop="responsibleType"
              label="责任人类型"
              width="150"
            >
              <template #header>
                <span style="color: #606266;font-weight: normal">责任人类型</span>
              </template>
              <template #default="scope">
                <el-form-item>
                  <el-select v-model="scope.row.responsibleType" placeholder="请选择" size="large" class="transparent-border-input" :disabled="isPaymentMode">
                    <el-option
                      v-for="dict in getIntDictOptions(DICT_TYPE.OEMS_PERSON_LIABLE_TYPE)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="liabilityType"
              label="责任类型"
              width="150"
            >
              <template #header>
                <span style="color: #606266;font-weight: normal">责任类型</span>
              </template>
              <template #default="scope">
                <el-form-item>
                  <el-select v-model="scope.row.liabilityType" placeholder="请选择" size="large" class="transparent-border-input" :disabled="isPaymentMode">
                    <el-option
                      v-for="dict in getIntDictOptions(DICT_TYPE.OEMS_LIABLE_TYPE)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="penaltyAmount"
              label="处罚金额"
              width="100"
            >
              <template #header>
                <span style="color: #606266;font-weight: normal">处罚金额</span>
              </template>
              <template #default="scope">
                <el-form-item>
                  <el-input v-model="scope.row.penaltyAmount" size="large" class="transparent-border-input center-input" :disabled="isPaymentMode" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="liabilityAmount"
              label="定责金额"
              width="100"
            >
              <template #header>
                <span style="color: #606266;font-weight: normal">定责金额</span>
              </template>
              <template #default="scope">
                <el-form-item>
                  <el-input v-model="scope.row.liabilityAmount" size="large" class="transparent-border-input center-input" :disabled="isPaymentMode" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="responsibleName"
              label="责任人名称"
              width="150"
            >
              <template #header>
                <span style="color: #606266;font-weight: normal">责任人名称</span>
              </template>
              <template #default="scope">
                <el-form-item>
                  <el-input v-model="scope.row.responsibleName" size="large" class="transparent-border-input center-input" :disabled="isPaymentMode" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="branchName"
              label="站点名称"
              width="150"
            >
              <template #header>
                <span style="color: #606266;font-weight: normal">站点名称</span>
              </template>
              <template #default="scope">
                <el-form-item>
                  <el-input v-model="scope.row.branchName" size="large" class="transparent-border-input center-input" :disabled="isPaymentMode" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="mobile"
              label="手机号码"
              width="150"
            >
              <template #header>
                <span style="color: #606266;font-weight: normal">手机号码</span>
              </template>
              <template #default="scope">
                <el-form-item>
                  <el-input v-model="scope.row.mobile" size="large" class="transparent-border-input center-input" :disabled="isPaymentMode" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="remark"
              label="备注"
              min-width="100"
            >
              <template #header>
                <span style="color: #606266;font-weight: normal">备注</span>
              </template>
              <template #default="scope">
                <el-form-item>
                  <el-input v-model="scope.row.remark" size="large" class="transparent-border-input center-input" :disabled="isPaymentMode" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" v-if="!isPaymentMode">
              <template #header>
                <span style="color: #606266;font-weight: normal">操作</span>
              </template>
              <template #default="scope">
                <div style="text-align: center">
                  <!-- 删除按钮：当有多行数据时显示 -->
                  <el-icon size="30px" color="#f25643" tabindex="0"
                           @click="deleteLiability(scope.$index)" @keydown.enter="deleteLiability(scope.$index)"
                           v-if="liabilityRecords.length > 1"
                           style="cursor: pointer; margin-right: 8px;"><Remove /></el-icon>
                  <!-- 添加按钮：在最后一行显示 -->
                  <el-icon size="30px" color="#1e83e9" tabindex="0"
                           @click="addLiability()" @keydown.enter="addLiability()"
                           v-if="scope.$index === liabilityRecords.length - 1"
                           style="cursor: pointer;"><CirclePlus /></el-icon>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </el-card>

      <!-- 理赔付款卡片 -->
      <el-card class="payment-card box-card">
        <template #header>
          <span>理赔付款</span>
        </template>
        <el-form :model="formData" :rules="formRules" ref="paymentForm" class="block-form" label-width="100px" label-position="right" inline>
          <el-form-item label="理赔付款人" prop="payer">
            <el-input style="width:200px" v-model="formData.payer" placeholder="请输入理赔付款人" class="underline-input" :disabled="isPaymentMode"/>
          </el-form-item>
          <el-form-item label="赔付金额" prop="payAmount">
            <el-input style="width:200px" v-model="formData.payAmount" placeholder="请输入赔付金额" class="underline-input" :disabled="isPaymentMode"/>
          </el-form-item>
          <el-form-item label="付款方式" prop="payType">
            <el-select v-model="formData.payType" placeholder="请选择付款方式" style="width:200px" :disabled="isPaymentMode">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.OEMS_CLAIM_PAY_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="赔付时间" prop="payTime">
            <el-date-picker
              v-model="formData.payTime"
              type="datetime"
              placeholder="选择日期时间"
              value-format="x"
              style="width:200px"
              :disabled="isPaymentMode"
            />
          </el-form-item>
          <el-form-item label="理赔收款人" prop="payeeName" required>
            <el-input style="width:200px" v-model="formData.collectPay.name" placeholder="请输入理赔收款人" class="underline-input" :disabled="isPaymentMode"/>
          </el-form-item>
          <el-form-item label="收款人手机" prop="payeeMobile" required>
            <el-input style="width:200px" v-model="formData.collectPay.mobile" placeholder="请输入收款人手机" class="underline-input" :disabled="isPaymentMode"/>
          </el-form-item>
          <el-form-item label="银行名称" prop="bankName" v-if="formData.payType == 1">
            <el-input style="width:200px" v-model="formData.collectPay.bankName" placeholder="请输入银行名称" class="underline-input" :disabled="isPaymentMode"/>
          </el-form-item>
          <el-form-item label="银行卡号" prop="bankCardNo" v-if="formData.payType == 1">
            <el-input style="width:200px" v-model="formData.collectPay.bankAccount" placeholder="请输入银行卡号" class="underline-input" :disabled="isPaymentMode"/>
          </el-form-item>
          <el-form-item  label="身份证号码" prop="idCardNo">
            <el-input style="width:200px" v-model="formData.collectPay.idCard" placeholder="请输入身份证号码" class="underline-input" :disabled="isPaymentMode"/>
          </el-form-item>
          <el-form-item label="付款备注" prop="payReason" style="width: 100%;">
            <el-input style="width: 200px" v-model="formData.payReason" placeholder="请输入付款备注" class="underline-input" :disabled="isPaymentMode"/>
          </el-form-item>
        </el-form>
      </el-card>

    </div>
  </div>
  <el-card class="bottom-bar" style="margin:0 -20px;border-bottom: none" :style="{ '--shadow-color': 'rgba(0, 0, 0, 0.1)' }">
    <el-row align="middle" style="justify-content: space-between">
      <div></div>
      <div class="action-buttons">
        <el-button @click="goBack">返回</el-button>
        <el-button @click="handleSave" type="primary" :loading="formLoading" style="width:90px" v-if="!isPaymentMode">保存</el-button>
        <el-button @click="handleFinish" type="primary" :loading="formLoading" style="width:90px" v-if="!isPaymentMode">处理完成</el-button>
        <el-button @click="handlePaymentConfirm" type="primary" :loading="formLoading" style="width:120px" v-if="isPaymentMode">理赔付款确认</el-button>
      </div>
    </el-row>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CopyDocument, Remove, CirclePlus } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/formatTime'
import { OrderClaimApi, OrderClaimVO } from '@/api/oems/orderclaim'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { getUserProfile } from '@/api/system/user/profile'
import { BranchApi } from '@/api/system/branch'
defineOptions({ name: 'OrderClaimProcess' })

const route = useRoute()
const router = useRouter()


// 响应式数据
const formData = ref({
  id: undefined,
  waybillId: undefined,
  waybillNo: undefined,
  claimStatus: undefined,
  claimType: undefined,
  applyId: undefined,
  applyOperationId: undefined,
  applyOperationName: undefined,
  applyTime: undefined,
  applyReason: undefined,
  applyAmount: undefined,
  applyImgUrl: undefined,
  revokeId: undefined,
  revokeName: undefined,
  revokeTime: undefined,
  revokeReason: undefined,
  approvalId: undefined,
  approvalName: undefined,
  approvalTime: undefined,
  remark: undefined,
  handleId: undefined,
  handleName: undefined,
  handleTime: undefined,
  payer: undefined,
  payTime: undefined,
  payReason: undefined,
  payAmount: undefined,
  payType: undefined,
  collectPayId: undefined,
  endId: undefined,
  endName: undefined,
  endTime: undefined,
  interceptStatus: undefined,
  errorNum: undefined,
  applicant: {
    name: undefined,
    mobile: undefined,
    idCard: undefined
  },
  collectPay: {
    name: undefined,
    mobile: undefined,
    bankName: undefined,
    bankAccount: undefined,
    idCard: undefined
  }
})

const communicationRecords = ref<any[]>([])
const liabilityRecords = ref<any[]>([])
const formLoading = ref(false)
const isPaymentMode = ref(false) // 是否为付款模式
const currentBranchName = ref('') // 当前用户站点名

// 表单引用
const communicationForm = ref()
const liabilityForm = ref()
const paymentForm = ref()

// 表单验证规则
const formRules = {
  payeeName: [
    { required: true, message: '请输入理赔收款人', trigger: 'blur' }
  ],
  payeeMobile: [
    { required: true, message: '请输入收款人手机', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 获取当前用户站点信息
const getCurrentUserBranch = async () => {
  try {
    const userProfile = await getUserProfile()
    if (userProfile.dept && userProfile.dept.id) {
      const branchInfo = await BranchApi.getBranchByDeptId(userProfile.dept.id)
      currentBranchName.value = branchInfo?.branchName || userProfile.dept.name || ''
    }
  } catch (error) {
    console.error('获取用户站点信息失败:', error)
    currentBranchName.value = ''
  }
}

// 获取理赔详情
const getClaimDetail = async () => {
  const id = route.query.id as string
  const mode = route.query.mode as string
  if (!id) {
    ElMessage.error('缺少理赔ID参数')
    return
  }

  try {
    formLoading.value = true
    formData.value = await OrderClaimApi.getOrderClaim(Number(id))

    // 确保 collectPay 对象存在
    if (!formData.value.collectPay) {
      formData.value.collectPay = {
        name: '',
        mobile: '',
        bankName: '',
        bankAccount: '',
        idCard: ''
      }
    }

    // 处理时间字段格式转换
    if (formData.value.payTime && typeof formData.value.payTime === 'string') {
      formData.value.payTime = new Date(formData.value.payTime).getTime()
    }

    // 判断是否为付款模式
    isPaymentMode.value = mode === 'payment' || formData.value.claimStatus === 5

    // 获取当前用户站点信息
    await getCurrentUserBranch()

    // 获取沟通记录和理赔定责数据
    await loadCommunicationRecords(Number(id))
    await loadLiabilityRecords(Number(id))
  } catch (error) {
    console.error('获取理赔详情失败:', error)
    ElMessage.error('获取理赔详情失败')
  } finally {
    formLoading.value = false
  }
}

// 加载沟通记录数据
const loadCommunicationRecords = async (orderClaimId: number) => {
  try {
    const records = await OrderClaimApi.getClaimRecordsListByOrderClaimId(orderClaimId)
    if (records && records.length > 0) {
      communicationRecords.value = records.map(record => ({
        replyBranchName: record.replyBranchName || '',
        replyContent: record.replyContent || '',
        replyTime: record.replyTime ? (typeof record.replyTime === 'string' ? new Date(record.replyTime).getTime() : record.replyTime) : '',
        remark: record.remark || ''
      }))
    } else {
      // 如果没有数据且不是付款模式，初始化一行空数据
      if (!isPaymentMode.value) {
        communicationRecords.value = [{
          replyBranchName: currentBranchName.value,
          replyContent: '',
          replyTime: Date.now(),
          remark: ''
        }]
      } else {
        communicationRecords.value = []
      }
    }
  } catch (error) {
    console.error('获取沟通记录失败:', error)
    // 出错时，如果不是付款模式则初始化一行空数据
    if (!isPaymentMode.value) {
      communicationRecords.value = [{
        replyBranchName: currentBranchName.value,
        replyContent: '',
        replyTime: Date.now(),
        remark: ''
      }]
    } else {
      communicationRecords.value = []
    }
  }
}

// 加载理赔定责数据
const loadLiabilityRecords = async (orderClaimId: number) => {
  try {
    const records = await OrderClaimApi.getClaimLiableListByOrderClaimId(orderClaimId)
    if (records && records.length > 0) {
      liabilityRecords.value = records.map(record => ({
        responsibleType: record.personLiableType,
        liabilityType: record.liableType,
        penaltyAmount: record.punishAmount || '',
        liabilityAmount: record.liableAmount || '',
        responsibleName: record.userName || '',
        branchName: record.branchName || '',
        mobile: record.mobile || '',
        remark: record.remark || ''
      }))
    } else {
      // 如果没有数据且不是付款模式，初始化一行空数据
      if (!isPaymentMode.value) {
        liabilityRecords.value = [{
          responsibleType: undefined,
          liabilityType: undefined,
          penaltyAmount: '',
          liabilityAmount: '',
          responsibleName: '',
          branchName: '',
          mobile: '',
          remark: ''
        }]
      } else {
        liabilityRecords.value = []
      }
    }
  } catch (error) {
    console.error('获取理赔定责失败:', error)
    // 出错时，如果不是付款模式则初始化一行空数据
    if (!isPaymentMode.value) {
      liabilityRecords.value = [{
        responsibleType: undefined,
        liabilityType: undefined,
        penaltyAmount: '',
        liabilityAmount: '',
        responsibleName: '',
        branchName: '',
        mobile: '',
        remark: ''
      }]
    } else {
      liabilityRecords.value = []
    }
  }
}

// 复制理赔编号
const copyClaimId = () => {
  navigator.clipboard.writeText(formData.value.id?.toString() || '')
  ElMessage.success('理赔编号已复制到剪贴板')
}

// 添加沟通记录
function addCommunication() {
  communicationRecords.value.push({
    replyBranchName: isPaymentMode.value ? '' : currentBranchName.value,
    replyContent: '',
    replyTime: isPaymentMode.value ? '' : Date.now(),
    remark: ''
  })
}

// 删除沟通记录
function deleteCommunication(index: number) {
  if (communicationRecords.value.length > 1) {
    communicationRecords.value.splice(index, 1)
  }
}

// 添加理赔定责
function addLiability() {
  liabilityRecords.value.push({
    responsibleType: undefined,
    liabilityType: undefined,
    penaltyAmount: '',
    liabilityAmount: '',
    responsibleName: '',
    branchName: '',
    mobile: '',
    remark: ''
  })
}

// 删除理赔定责
function deleteLiability(index: number) {
  if (liabilityRecords.value.length > 1) {
    liabilityRecords.value.splice(index, 1)
  }
}

// 返回列表
const goBack = () => {
  router.back()
}

// 保存处理信息
const handleSave = async () => {
  try {
    // 验证表单
    await Promise.all([
      communicationForm.value?.validate(),
      liabilityForm.value?.validate(),
      paymentForm.value?.validate()
    ])

    formLoading.value = true

    // 构建保存数据
    const saveData = {
      ...formData.value,
      claimStatus: 4, // 处理中状态
      claimRecordss: communicationRecords.value.map(record => ({
        orderClaimId: formData.value.id,
        replyBranchName: record.replyBranchName,
        replyContent: record.replyContent,
        replyTime: record.replyTime,
        remark: record.remark
      })),
      claimLiables: liabilityRecords.value.map(record => ({
        orderClaimId: formData.value.id,
        personLiableType: record.responsibleType,
        liableType: record.liabilityType,
        punishAmount: record.penaltyAmount,
        liableAmount: record.liabilityAmount,
        userName: record.responsibleName,
        branchName: record.branchName,
        mobile: record.mobile,
        remark: record.remark
      })),
      collectPay: {
        name: formData.value.collectPay?.name || '',
        mobile: formData.value.collectPay?.mobile || '',
        bankName: formData.value.collectPay?.bankName || '',
        bankAccount: formData.value.collectPay?.bankAccount || '',
        idCard: formData.value.collectPay?.idCard || ''
      }
    }

    await OrderClaimApi.updateOrderClaim(saveData)
    ElMessage.success('保存成功')

  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    formLoading.value = false
  }
}

// 理赔结束
const handleFinish = async () => {
  try {
    await ElMessageBox.confirm('确认结束理赔处理吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 验证表单
    await Promise.all([
      communicationForm.value?.validate(),
      liabilityForm.value?.validate(),
      paymentForm.value?.validate()
    ])

    formLoading.value = true

    // 构建保存数据
    const saveData = {
      ...formData.value,
      claimStatus: 5, // 已处理状态
      claimRecordss: communicationRecords.value.map(record => ({
        orderClaimId: formData.value.id,
        replyBranchName: record.replyBranchName,
        replyContent: record.replyContent,
        replyTime: record.replyTime,
        remark: record.remark
      })),
      claimLiables: liabilityRecords.value.map(record => ({
        orderClaimId: formData.value.id,
        personLiableType: record.responsibleType,
        liableType: record.liabilityType,
        punishAmount: record.penaltyAmount,
        liableAmount: record.liabilityAmount,
        userName: record.responsibleName,
        branchName: record.branchName,
        mobile: record.mobile,
        remark: record.remark
      })),
      collectPay: {
        name: formData.value.collectPay?.name || '',
        mobile: formData.value.collectPay?.mobile || '',
        bankName: formData.value.collectPay?.bankName || '',
        bankAccount: formData.value.collectPay?.bankAccount || '',
        idCard: formData.value.collectPay?.idCard || ''
      }
    }

    await OrderClaimApi.updateOrderClaim(saveData)
    ElMessage.success('理赔处理完成')
    goBack()

  } catch (error) {
    console.error('理赔结束失败:', error)
    ElMessage.error('理赔结束失败')
  } finally {
    formLoading.value = false
  }
}

// 理赔付款确认
const handlePaymentConfirm = async () => {
  try {
    await ElMessageBox.confirm('确认理赔付款吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    formLoading.value = true

    // 构建保存数据
    const saveData = {
      ...formData.value,
      claimStatus: 6, // 已结束状态
      claimRecordss: communicationRecords.value.map(record => ({
        orderClaimId: formData.value.id,
        replyBranchName: record.replyBranchName,
        replyContent: record.replyContent,
        replyTime: record.replyTime,
        remark: record.remark
      })),
      claimLiables: liabilityRecords.value.map(record => ({
        orderClaimId: formData.value.id,
        personLiableType: record.responsibleType,
        liableType: record.liabilityType,
        punishAmount: record.penaltyAmount,
        liableAmount: record.liabilityAmount,
        userName: record.responsibleName,
        branchName: record.branchName,
        mobile: record.mobile,
        remark: record.remark
      })),
      collectPay: {
        name: formData.value.collectPay?.name || '',
        mobile: formData.value.collectPay?.mobile || '',
        bankName: formData.value.collectPay?.bankName || '',
        bankAccount: formData.value.collectPay?.bankAccount || '',
        idCard: formData.value.collectPay?.idCard || ''
      }
    }

    await OrderClaimApi.updateOrderClaim(saveData)
    ElMessage.success('理赔付款确认完成')
    goBack()

  } catch (error) {
    console.error('理赔付款确认失败:', error)
    ElMessage.error('理赔付款确认失败')
  } finally {
    formLoading.value = false
  }
}


// 页面初始化
onMounted(() => {
  getClaimDetail()
})
</script>

<style scoped>
.page-container {
  background-color: #f5f5f5;
}

.content-wrapper {
  padding: 0 5px;
  max-width: 100%;
  box-sizing: border-box;
}

/* 顶部栏样式 */
.top-bar {
  .claim-info {
    color: #f25643;
  }
  display: flex;
  align-items: flex-end;
}

.title {
  font-size: 20px;
}

/* 卡片通用样式 */
.box-card {
  margin-bottom: 10px;
  border-radius: 6px;
  box-shadow: none;
  border: none;
}

.communication-card {
  border-radius: 6px;
  box-shadow: none;
  border: none;
}

.liability-card {
  border-radius: 6px;
  box-shadow: none;
  border: none;
}

.payment-card {
  border-radius: 6px;
  box-shadow: none;
  border: none;
}

/* 移除标题与内容之间的分割线 */
:deep(.el-card__header) {
  padding: 15px 20px 0;
  border-bottom: none;
  margin-bottom: -10px;
}

/* 表格样式 */
:deep(.el-table) {
  margin-top: 10px;
  width: 100% !important;
  table-layout: fixed;
}

:deep(.el-table th) {
  font-weight: 500;
  text-align: center;
}

/* 表格单元格居中 */
:deep(.center-cell-table .el-table__body td) {
  text-align: center;
}

/* 透明边框输入框 */
:deep(.transparent-border-input .el-input__wrapper) {
  border: none;
  box-shadow: none;
  background: transparent;
}

:deep(.transparent-border-input .el-textarea__inner) {
  border: none;
  box-shadow: none;
  background: transparent;
}

:deep(.transparent-border-input .el-select .el-input__wrapper) {
  border: none;
  box-shadow: none;
  background: transparent;
}

/* 居中输入框 */
:deep(.center-input .el-input__inner) {
  text-align: center;
}

/* 底部栏样式 */
.bottom-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: white;
  box-shadow: 0 -2px 10px var(--shadow-color);
  padding: 0 20px 0 20px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 下划线输入框样式 */
:deep(.underline-input .el-input__wrapper) {
  border: none;
  border-bottom: 1px solid #dcdfe6;
  border-radius: 0;
  box-shadow: none;
  background: transparent;
}

:deep(.underline-input .el-input__wrapper:hover) {
  border-bottom-color: #c0c4cc;
}

:deep(.underline-input .el-input__wrapper.is-focus) {
  border-bottom-color: #409eff;
}

:deep(.underline-input .el-textarea__inner) {
  border: none;
  border-bottom: 1px solid #dcdfe6;
  border-radius: 0;
  box-shadow: none;
  background: transparent;
}

:deep(.underline-input .el-textarea__inner:hover) {
  border-bottom-color: #c0c4cc;
}

:deep(.underline-input .el-textarea__inner:focus) {
  border-bottom-color: #409eff;
}

:deep(.underline-input .el-select .el-input__wrapper) {
  border: none;
  border-bottom: 1px solid #dcdfe6;
  border-radius: 0;
  box-shadow: none;
  background: transparent;
}

:deep(.underline-input .el-date-editor .el-input__wrapper) {
  border: none;
  border-bottom: 1px solid #dcdfe6;
  border-radius: 0;
  box-shadow: none;
  background: transparent;
}

/* 针对表格中的表单项 */
:deep(.el-table .el-form-item) {
  margin-bottom: 0 !important;
  display: flex;
  align-items: center;
  height: 100%;
}

/* 调整输入框高度与垂直对齐 */
:deep(.transparent-border-input .el-input) {
  height: 100% !important;
  display: flex;
  align-items: center;
}

/* 修复表格单元格内边距导致的错位 */
:deep(.el-table__cell) {
  padding: 8px 0 !important;
}

/* 调整验证错误提示的位置，确保不被覆盖 */
:deep(.el-table .el-form-item__error) {
  position: static !important;
  margin-top: 4px;
  text-align: center;
  width: 100%;
  font-size: 12px;
}

/* 添加按钮样式 */
.mt-3 {
  margin-top: 12px;
}

:deep(.el-button.is-round) {
  border-radius: 20px;
  padding: 8px 20px;
}

/* 表单项样式 */
:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-form-item__error) {
  position: static;
  margin-top: 2px;
}

/* 日期选择器样式 */
:deep(.el-date-editor) {
  width: 100%;
}

:deep(.el-date-editor .el-input__wrapper) {
  border: none;
  box-shadow: none;
  background: transparent;
}

/* 选择器样式 */
:deep(.el-select) {
  width: 100%;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .content-wrapper {
    padding: 0;
  }

  .button-group {
    flex-direction: column;
    align-items: center;
  }

  .button-group .el-button {
    width: 200px;
  }
}
</style>
