<template>
  <!-- 顶部栏，紧贴卡片 -->
  <div class="header-bar">
    <div class="order-no">
      <span>运单号：{{ formData.waybillNo || waybillNo }}</span>
      <el-icon style="margin-left: 8px;color:#606266;cursor:pointer"  @click="copyWaybillNo"><CopyDocument /></el-icon>
    </div>
    <div class="title">运单详情</div>
    <div class="time">开单时间：{{ formatToDateTime(formData.createTime) }}</div>
  </div>
  <el-card  style="border: none;box-shadow: none">
    <!-- tab栏 -->
    <div class="tab-bar">
      <div class="tab-list">
        <div
          v-for="(tab, i) in tabList"
          :key="tab.text" style="cursor: pointer"
          :class="['tab', {active: i === activeTabIndex}]"
          @click="activeTabIndex = i"
        >
          {{tab.text}}
        </div>
      </div>
      <div class="custom-steps-bar">
        <div class="custom-steps-flex">
          <template v-for="(step, i) in currentStepList" :key="step.text">
            <div class="custom-step-flex">
              <div :class="['custom-step-label', { active: step.done }]">{{ step.text }}</div>
              <el-icon :class="['custom-step-icon', {active: step.done}]">
                <CircleCheck v-if="step.done" />
              </el-icon>
            </div>
            <div
              v-if="i < currentStepList.length - 1"
              :class="['custom-step-line-flex', { active: currentStepList[i + 1].done }]"
            ></div>
          </template>
        </div>
      </div>
    </div>
    <div v-if="activeTabIndex === 0">
    <!-- 基础信息 -->
    <el-form class="block-form" label-width="100px" label-position="right" inline label-suffix=":">
      <div class="section-title-row">
        <span class="section-title">基础信息</span>
        <span class="section-right">
          {{ formData.transportType === 1 ? '零担物流' : '直提直送' }}
        </span>
      </div>
      <el-form-item label="发货部门"><span class="form-value">{{ formData.startDeptName }}</span></el-form-item>
      <el-form-item label="到达部门"><span class="form-value">{{ formData.endDeptName }}</span></el-form-item>
      <el-form-item label="目的网点">
        <span class="form-value">{{ formData.destinationName }}</span>
        <span class="form-value" style="margin-left: 8px;color:#909399;">
          {{ formData.isTransfer === true ? '有外转' : '无外转' }}
        </span>
      </el-form-item>
      <!-- 换行，运输路由单独一行 -->
      <div style="flex-basis: 100%; height: 0;"></div>
      <el-form-item label="运输路由">
        <template v-for="(step, index) in steps" :key="index">
          <span style="text-align: center">{{step.routeDeptName}}</span>
        <img v-if="index < steps.length - 1" width="30px" src="https://baidalt.com.cn:9000/baidatms/Right.png" style="margin:0 10px 0 10px"/>
      </template></el-form-item>
    </el-form>
    <!-- 发货方/收货方 -->
    <div class="form-row" style="align-items: stretch;">
      <el-form class="block-form" label-width="100px" label-position="right" inline style="flex:1;" label-suffix=":">
        <div class="section-title" style="width: 100%; font-size: 15px;">发货方</div>
        <el-form-item label="发货人"><span class="form-value">{{ formData.shipperName }}</span></el-form-item>
        <el-form-item label="发货手机"><span class="form-value">{{ formData.shipperMobile }}</span></el-form-item>
        <el-form-item label="身份证号"><span class="form-value">{{ formData.shipperIdCard }}</span></el-form-item>
      </el-form>
      <el-form class="block-form" label-width="100px" label-position="right" inline style="flex:1; min-width:260px;" label-suffix=":">
        <div class="section-title" style="width: 100%; font-size: 15px;">收货方</div>
        <el-form-item label="收货人"><span class="form-value">{{ formData.consigneeName }}</span></el-form-item>
        <el-form-item label="收货手机"><span class="form-value">{{ formData.consigneeMobile }}</span></el-form-item>
        <el-form-item label="详细地址"><span class="form-value">{{ formData.consigneeDetailAddress }}</span></el-form-item>
      </el-form>
    </div>
    <!-- 货品信息 -->
    <el-form class="block-form" inline label-suffix=":">
      <div class="section-title" style="width: 100%;">货品信息</div>
      <div class="table-section" style="width: 100%;">
        <el-table
          :data="formData.goodsList"
          border
          style="width: 100%;"
          :header-cell-style="{ color: '#606266', textAlign: 'center', fontWeight: 400 }"
          :cell-style="{ textAlign: 'center' }"
        >
          <el-table-column prop="goodsName" label="货品名称" />
          <el-table-column prop="goodsType" label="货品类型" />
          <el-table-column prop="goodsPackageUnit" label="包装" />
          <el-table-column prop="goodsNum" label="总数量(件数)" />
          <el-table-column prop="goodsWeight" label="总重量(公斤)" />
          <el-table-column prop="goodsVolume" label="总体积(方)" />
          <el-table-column prop="remark" label="货品备注" />
        </el-table>
      </div>
    </el-form>

    <div v-if="formData.transportType === 1">
    <!-- 增值服务 -->
    <el-form class="block-form" label-width="100px" label-position="right"  label-suffix=":">
      <div class="section-title-row">
        <span class="section-title">增值服务</span>
        <span class="section-right">
          <!-- 这里可根据实际数据调整 -->
          {{ getValueAddedService() }}
        </span>
      </div>
      <el-form-item label="揽货方式">
        <span class="form-value">{{ formData.takeDelivery === true ? '上门揽货' : '无' }}
          <template v-if="formData.receiveTime">（{{ formatToDateTime(formData.receiveTime) }}）</template>
        </span>
      </el-form-item>
      <el-form-item label="控货方式">
        <span class="form-value">{{ formData.waitReleaseGoods === true ? '等通知放货' : '不控货' }}</span>
      </el-form-item>
      <el-form-item label="送货方式">
        <span class="form-value">
            {{ getDictLabel("deliver_type", formData.deliveryType) }}
          <template v-if="formData.deliveryTime">（{{ formatToDateTime(formData.deliveryTime) }}）</template>
        </span>
      </el-form-item>
      <el-form-item label="回单类型">
        <span class="form-value">
          {{ getDictLabel("receipt_type", formData.receiptType) }}（{{ formData.receiptNum }}份）
        </span>
      </el-form-item>
      <el-form-item label="保价额">
        <span class="form-value">￥{{ formData.baoxianfei || '0' }}</span>
      </el-form-item>
      <el-form-item label="代收货款">
        <span class="form-value">￥{{ formData.collectPayments || '0' }}</span>
      </el-form-item>
    </el-form>

    <!-- 费用信息 -->
    <el-form class="block-form" label-width="100px" label-position="right" inline label-suffix=":">
      <div class="section-title" style="width: 100%;">费用信息</div>
      <div class="fee-row">
        <el-form-item label="付款方式">
          <span>
            {{ payType || '' }}
          </span>
        </el-form-item>
        <el-form-item >
          <span class="fee-red">
            总费用：￥{{ feiyongHj || '0' }}
            （发方付：￥{{ xianfuHj || '0' }}，收方付：￥{{ tifuHj|| '0' }}）
          </span>
          <span class="fee-gray">
            标准运费：￥{{ formData.biaozhunYf || '0' }}，折扣运费：￥{{ formData.zhekouYf || '0' }}
          </span>
        </el-form-item>
      </div>
      <div class="fee-divider"></div>
      <el-form-item label="揽货费"><span class="form-value">￥{{ formData.jiehuofei || '0' }}（{{getDictLabel('settlement_type', formData.jiehuofeiPt)}}）</span></el-form-item>
      <el-form-item label="控货费"><span class="form-value">￥{{ formData.konghuofei || '0' }}（{{getDictLabel('settlement_type', formData.jiehuofeiPt)}}）</span></el-form-item>
      <el-form-item label="送货费"><span class="form-value">￥{{ formData.songhuofei || '0' }}（{{getDictLabel('settlement_type', formData.songhuofeiPt)}}）</span></el-form-item>
      <el-form-item label="回单费"><span class="form-value">￥{{ formData.huidanfei || '0' }}（{{getDictLabel('settlement_type', formData.huidanfeiPt)}}）</span></el-form-item>
      <el-form-item label="保价费"><span class="form-value">￥{{ formData.baozhifei || '0' }}（{{getDictLabel('settlement_type', formData.baozhifeiPt)}}）</span></el-form-item>
      <el-form-item label="代收手续费"><span class="form-value">￥{{ formData.collectHandlingFee || '0' }}（{{payType}}）</span></el-form-item>
      <el-form-item label="制单费"><span class="form-value">￥{{ formData.zhidanfei || '0' }}（{{getDictLabel('settlement_type', formData.zhidanfeiPt)}}）</span></el-form-item>
      <el-form-item label="外转费"><span class="form-value">￥{{ formData.waizhuanfei || '0' }}（{{getDictLabel('settlement_type', formData.waizhuanfeiPt)}}）</span></el-form-item>
      <el-form-item label="其他费用"><span class="form-value">￥{{ formData.other1 || '0' }}（{{getDictLabel('settlement_type', formData.other1Pt)}}）</span></el-form-item>
      <el-form-item label="佣金"><span class="form-value">￥{{ formData.yongjin || '0' }}（{{getDictLabel('settlement_type1', formData.yongjinPt)}}）</span></el-form-item>
    </el-form>

      <el-form class="remark-form" style="border: 1px solid #ebeef5;border-radius: 6px;padding: 14px;margin-bottom: 16px;" label-width="100px" label-position="right" label-suffix=":" >
        <el-row :gutter="40" style="margin-bottom: -20px">
          <el-col :span="12">
            <el-form-item label="客户备注" >
              <span>{{formData.customerRemark}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司备注" >
              <span>{{formData.companyRemark}}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

    <!-- 按钮组 -->
    <el-form class="block-form button-group-form" inline>
      <div class="button-group-right">
        <el-button plain @click="gotoBack" class="action-btn">返回</el-button>
        <el-button plain @click="gotoError" class="action-btn">异常</el-button>
        <el-button plain class="action-btn">回单</el-button>
        <el-button plain @click="gotoQS" class="action-btn">签收</el-button>
        <el-button plain @click='delivery' class="action-btn">送货</el-button>
        <el-button plain v-if="showTransfer" @click="transferFormRef" class="action-btn">转货</el-button>
        <el-button plain class="action-btn">作废</el-button>
        <el-button plain @click="openDeleteDialog" class="action-btn">删除</el-button>
        <el-button plain @click="update" class="action-btn">修改</el-button>
        <el-button plain @click="handlePrint" class="action-btn">打印</el-button>
        <el-button type="primary" @click="goToVoyage" class="action-btn">配载</el-button>
      </div>
    </el-form>
    </div>


    <div v-if="formData.transportType === 2">
      <!-- 车辆信息部分 -->
      <el-form class="block-form" label-width="100px" label-position="right" inline  label-suffix=":">
        <div class="section-title-row">
          <span class="section-title">车辆信息</span>
        </div>
        <el-form-item label="服务">
          整车直提直送/拼车直提直送
        </el-form-item>

        <el-form-item label="车型">
            <span>4.2米，</span>
            <span>厢式/高栏/平板</span>
        </el-form-item>
      </el-form>

      <!-- 增值服务 -->
      <el-form class="block-form" label-width="100px" label-position="right"  label-suffix=":">
        <div class="section-title-row">
          <span class="section-title">增值服务</span>
          <span class="section-right">
          <!-- 这里可根据实际数据调整 -->
          {{ getValueAddedService() }}
        </span>
        </div>
        <el-form-item label="揽货时间">
        <span class="form-value">
          {{ formatToDateTime(formData.receiveTime) }}
        </span>
        </el-form-item>
        <el-form-item label="送货时间">
        <span class="form-value">
          {{ formatToDateTime(formData.deliveryTime) }}
        </span>
        </el-form-item>
        <el-form-item label="回单类型">
        <span class="form-value">
          {{ getDictLabel("receipt_type", formData.receiptType) }}（{{ formData.receiptNum }}份）
        </span>
        </el-form-item>
        <el-form-item label="保价额">
          <span class="form-value">￥{{ formData.baoxianfei || '0' }}</span>
        </el-form-item>
        <el-form-item label="代收货款">
          <span class="form-value">￥{{ formData.collectPayments || '0' }}</span>
        </el-form-item>
      </el-form>

      <!-- 费用信息 -->
      <el-form class="block-form" label-width="100px" label-position="right" inline label-suffix=":">
        <div class="section-title" style="width: 100%;">费用信息</div>
        <div class="fee-row">
          <el-form-item label="付款方式">
          <span>
            现付/到付/回单付
          </span>
          </el-form-item>
          <el-form-item >
          <span class="fee-red">
            总费用：￥{{ feiyongHj || '0' }}
            （发方付：￥{{ xianfuHj || '0' }}，收方付：￥{{ tifuHj|| '0' }}）
          </span>
            <span class="fee-gray">
            标准运费：￥{{ formData.biaozhunYf || '0' }}，折扣运费：￥{{ formData.zhekouYf || '0' }}
          </span>
          </el-form-item>
        </div>
        <div class="fee-divider"></div>
        <el-form-item label="揽货费"><span class="form-value">￥{{ formData.jiehuofei || '0' }}（{{getDictLabel('settlement_type', formData.jiehuofeiPt)}}）</span></el-form-item>
        <el-form-item label="送货费"><span class="form-value">￥{{ formData.songhuofei || '0' }}（{{getDictLabel('settlement_type', formData.songhuofeiPt)}}）</span></el-form-item>
        <el-form-item label="回单费"><span class="form-value">￥{{ formData.huidanfei || '0' }}（{{getDictLabel('settlement_type', formData.huidanfeiPt)}}）</span></el-form-item>
        <el-form-item label="保价费"><span class="form-value">￥{{ formData.baozhifei || '0' }}（{{getDictLabel('settlement_type', formData.baozhifeiPt)}}）</span></el-form-item>
        <el-form-item label="代收手续费"><span class="form-value">￥{{ formData.collectHandlingFee || '0' }}（{{payType}}）</span></el-form-item>
        <el-form-item label="制单费"><span class="form-value">￥{{ formData.zhidanfei || '0' }}（{{getDictLabel('settlement_type', formData.zhidanfeiPt)}}）</span></el-form-item>
        <el-form-item label="其他费用"><span class="form-value">￥{{ formData.other1 || '0' }}（{{getDictLabel('settlement_type', formData.other1Pt)}}）</span></el-form-item>
        <el-form-item label="信源订金"><span class="form-value">￥{{'0' }}（可退）</span></el-form-item>
        <el-form-item label="信源服务费"><span class="form-value">￥{{'0' }}（不可退）</span></el-form-item>
      </el-form>

      <!-- 按钮组 -->
      <el-form class="block-form button-group-form" inline>
        <div class="button-group-right">
          <el-button plain @click="gotoBack" class="action-btn">返回</el-button>
          <el-button plain @click="gotoError" class="action-btn">异常</el-button>
          <el-button plain class="action-btn">回单</el-button>
          <el-button plain @click="gotoQS" class="action-btn">签收</el-button>
          <el-button plain @click='delivery' class="action-btn">送货</el-button>
          <el-button plain class="action-btn">收款</el-button>
          <el-button plain class="action-btn">作废</el-button>
          <el-button plain @click="openDeleteDialog" class="action-btn">删除</el-button>
          <el-button plain @click="update" class="action-btn">修改</el-button>
          <el-button type="primary" @click="goToVoyage" class="action-btn">配载</el-button>
        </div>
      </el-form>
    </div>
    </div>
    <div v-else-if="activeTabIndex === 1">
      <!-- 运单跟踪内容 -->
      <ContentWrap>
        <el-table
          :data="trickList"
          style="width: 100%;"
          :header-cell-style="{ color: '#606266', textAlign: 'center', fontWeight: 400 }"
          :cell-style="{ textAlign: 'center' }"
        >
          <el-table-column prop="eventNodeValue" label="操作节点" />
          <el-table-column prop="content" label="操作内容" />
          <el-table-column prop="deptName" label="操作部门" />
          <el-table-column prop="userName" label="操作员" />
          <el-table-column prop="createTime" :formatter="dateFormatter" label="操作时间" />
        </el-table>
      </ContentWrap>
    </div>
    <div v-else-if="activeTabIndex === 2">
      <ReceiptTransport  :receiptTransport="receiptTransport"
                        :receiptTrackList="receiptTrackList" />
    </div>

    <div v-else-if="activeTabIndex === 4">
      <WaybillEditData  :waybillNo="formData.waybillNo" :editDataList="editDataList" />
    </div>
    <div v-else-if="activeTabIndex === 5">
      <WaybillErrorForm :waybillNo="waybillNo" :errorList="errorList" />
    </div>
    <div v-else-if="activeTabIndex === 6">
      <WaybillFinanceRecord :waybillNo="waybillNo" :financeList="financeList" />
    </div>

  </el-card>

  <DeleteApplyForm ref="deleteApplyFormRef" @success="onDeleteSuccess" />
  <WaybillTransferForm ref="formRefWaybill"/>
  <WaybillForm ref="formQSWaybill" />
  <OrderErrorAddForm ref="errorFormRefAdd" :loginDept="loginDept" />
</template>

<script setup lang="ts">
import { onMounted, ref, nextTick, computed } from 'vue'
import { CopyDocument, CircleCheck } from '@element-plus/icons-vue'
import * as DeptApi from '@/api/system/dept'
import { useRoute, useRouter } from 'vue-router'
import { WaybillApi, WaybillVO } from '@/api/wbms/waybill'
import { RouteApi } from '@/api/system/route'
import { FreightSchemeApi } from '@/api/fee/freightscheme'
import { getDictLabel} from '@/utils/dict'
import { formatToDateTime } from '@/utils/dateUtil'
import { EventRouteApi, EventRouteVO } from '@/api/wbms/eventroute'
import { dateFormatter } from '@/utils/formatTime'
import { PrintSettingApi } from '@/api/system/print/printsetting'
import { getLodop } from '@/utils/LodopFuncs.js'
import DeleteApplyForm from '@/views/wbms/deleteapply/DeleteApplyForm.vue'
import { DeleteApplyApi } from '@/api/wbms/deleteapply'
import WaybillTransferForm from '@/views/tms/transfer/waybill/components/WaybillTransferForm.vue'
import WaybillForm from '@/views/tms/sign/waybill/WaybillForm.vue'
import OrderErrorAddForm from '@/views/oems/ordererror/OrderErrorAddForm.vue'
import ReceiptInfo from '@/views/wbms/componse/ReceiptInfo.vue'
import { ReceiptInfoApi } from '@/api/wbms/receipt'
import ReceiptTransport from '@/views/wbms/componse/ReceiptTransport.vue'
import WaybillEditData from '@/views/wbms/componse/WaybillEditData.vue'
import WaybillErrorForm from '@/views/oems/ordererror/WaybillErrorForm.vue'
import WaybillFinanceRecord from '@/views/wbms/componse/WaybillFinanceRecord.vue'
import { EditApi } from '@/api/wbms/edit'
import { OrderErrorApi } from '@/api/oems/ordererror'
import { NetworkCalculateLogApi } from '@/api/finance/networkcalculatelog'
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const router = useRouter()
const route = useRoute()
const loginDept = ref()
const waybillNo = ref()
const feiyongHj = ref(0)
const xianfuHj = ref(0)
const tifuHj = ref(0)
const copyWaybillNo = () => {
  console.log("点击复制")
  const text = formData.value.waybillNo || waybillNo.value
  if (navigator.clipboard && navigator.clipboard.writeText) {
    navigator.clipboard.writeText(text).then(() => {
      message.success('运单号已复制')
    }).catch(() => {
      message.error('复制失败，请手动复制')
    })
  } else {
    // 兼容老浏览器
    const input = document.createElement('input')
    input.value = text
    document.body.appendChild(input)
    input.select()
    try {
      document.execCommand('copy')
      message.success('运单号已复制')
    } catch (e) {
      message.error('复制失败，请手动复制')
    }
    document.body.removeChild(input)
  }
}
const computeFee = async () => {
  if (formData.value.endDeptId == undefined) {
    return
  } else {
    const data = formData.value as unknown as WaybillVO
    const fee = await WaybillApi.computeFeeHj(data)
    feiyongHj.value = fee.feiyongHj
    xianfuHj.value = fee.xianfuHj
    tifuHj.value = fee.tifuHj
  }
}

const payType = computed(() => {
  if (formData.value.xianfuYf && formData.value.xianfuYf > 0) {
    return getDictLabel('settlement_type', '0')
  }
  if (formData.value.tifuYf && formData.value.tifuYf > 0) {
    return getDictLabel('settlement_type', '1')
  }
  if (formData.value.xianfuyuejieYf && formData.value.xianfuyuejieYf > 0) {
    return getDictLabel('settlement_type', '2')
  }
  if (formData.value.huidanfuYf && formData.value.huidanfuYf > 0) {
    return getDictLabel('settlement_type', '3')
  }
  if (formData.value.koufuYf && formData.value.koufuYf > 0) {
    return getDictLabel('settlement_type', '4')
  }
  return '无'
})
const tabList = [
  { text: '运单信息'},
  { text: '运单跟踪'},
  { text: '回单跟踪'},
  { text: '运单记录'},
  { text: '改单记录'},
  { text: '异常记录'},
  { text: '财务记录'}
]
const activeTabIndex = ref(0)

const statusMap = {
  loading: [1, 2, 12, 13],
  shipping: [3, 4,8,9, 12, 13, 14, 15, 16],
  arrived: [5, 10, 11],
  signed: [6, 17, 18, 19, 20 ,21]
}
const getCurrentStep = (status: number) => {
  if (statusMap.loading.includes(status)) return 0
  if (statusMap.shipping.includes(status)) return 1
  if (statusMap.arrived.includes(status)) return 2
  if (statusMap.signed.includes(status)) return 3
  return -1 // 未命中任何状态
}
const stepLabels = ['装车', '运输', '到达', '签收']
const stepList = computed(() => {
  const status = Number(formData.value.waybillStatus)
  const currentStep = getCurrentStep(status)
  // 如果currentStep为-1，所有都不亮
  return stepLabels.map((text, idx) => ({
    text,
    done: currentStep >= 0 && idx <= currentStep
  }))
})

// 财务记录步骤
const financeStepLabels = ['已结算', '已审核', '已归档', '已划转']
const financeStepList = computed(() => {
  const status = Number(formData.value.networkCalcStatus || 0)
  // 状态映射：2-已结算, 3-已审核, 4-已归档, 6-已划转
  const statusMap = [2, 3, 4, 6] // 对应financeStepLabels的索引
  return financeStepLabels.map((text, idx) => ({
    text,
    done: status >= statusMap[idx]
  }))
})

// 根据当前tab显示不同的步骤
const currentStepList = computed(() => {
  if (activeTabIndex.value === 6) { // 财务记录tab
    return financeStepList.value
  }
  return stepList.value
})

const formData = ref({
  flag: '1',
  id: undefined,
  waybillNo: undefined,
  orderNo: undefined,
  orderId: undefined,
  waybillStatus: undefined,
  transportType: 1,
  isTransfer: false,
  shipperId: undefined,
  consigneeId: undefined,
  pickUpType: 0,
  isDelivery: false,
  deliveryType: '0',
  deliveryTime:undefined,
  expenseId: undefined,
  takeDelivery: false,
  receiveTime:undefined,
  waitReleaseGoods: false,
  existReceipt: true,
  receiptType: 1,
  receiptNum: 1,
  positionId: undefined,
  shipperPay: undefined,
  consigneePay: undefined,
  totalCost: undefined,
  orderRefNo: undefined,
  existPicture: undefined,
  existRemark: undefined,
  companyRemark: undefined,
  customerRemark: undefined,
  deptId: undefined,
  userId: undefined,
  startDeptId: undefined,
  startDeptName: undefined,
  endDeptId: undefined,
  endDeptName: undefined,
  endDeptBranchType: undefined,
  endDeptParentId: undefined,
  destination: undefined,
  destinationName: undefined,
  waybillImgUrl: undefined,
  goodsList: [
    {
      goodsNo: 0,
      goodsName: undefined,
      goodsType: undefined,
      goodsPackageUnit: undefined,
      goodsNum: undefined,
      goodsVolume: undefined,
      goodsWeight: undefined,
      goodsPrice: undefined,
      remark: undefined
    }
  ],
  shipper: {
    name: undefined,
    mobile: undefined,
    areaId: undefined,
    detailAddress: undefined,
    idCard: undefined,
    bankName: undefined,
    bankAccount: undefined,
    customerNumber: undefined
  },
  consignee: {
    name: undefined,
    mobile: undefined,
    areaId: undefined,
    detailAddress: undefined,
    customerNumber: undefined
  },
  shipperName: undefined,
  shipperMobile: undefined,
  shipperAreaId: undefined,
  shipperDetailAddress: undefined,
  shipperIdCard: undefined,
  shipperBankName: undefined,
  shipperBankAccount: undefined,
  customerNumber: undefined,
  consigneeName: undefined,
  consigneeMobile: undefined,
  consigneeAreaId: undefined,
  consigneeDetailAddress: undefined,
  biaozhunYf: 0,
  zhekouYf: undefined,
  zhehouYf: undefined,
  huidanfuYf: undefined,
  koufuYf: undefined,
  multipleSelectiontifuYf: undefined,
  tifuYf: undefined,
  tifuyuejieYf: undefined,
  xianfuYf: undefined,
  xianfuyuejieYf: undefined,
  baoxianfei: undefined,
  baoxianfeiFl: 1,
  baozhifei: undefined,
  baozhifeiPt: '0',
  dianfufei: undefined,
  dianfufeiPt: '0',
  jiehuofei: undefined,
  jiehuofeiPt: '0',
  songhuofei: undefined,
  songhuofeiPt: '0',
  zhidanfei: undefined,
  zhidanfeiPt: '0',
  huidanfei: undefined,
  huidanfeiPt: '0',
  konghuofei: 0,
  konghuofeiPt: '0',
  waizhuanfei: undefined,
  waizhuanfeiPt: '0',
  peisongfei: undefined,
  yongjin: undefined,
  yongjinPt: '0',
  yongjinSkr: undefined,
  yongjinAccount: undefined,
  yongjinMobile: undefined,
  other1: undefined,
  other1Pt: '0',
  other2: undefined,
  other2Pt: '0',
  other3: undefined,
  other3Pt: '0',
  other4: undefined,
  other4Pt: '0',
  other5: undefined,
  other5Pt: '0',
  other6: undefined,
  other6Pt: '0',
  other7: undefined,
  other7Pt: '0',
  other8: undefined,
  other8Pt: '0',
  other9: undefined,
  other9Pt: '0',
  routeBranchName: undefined,
  collectBankName: undefined,
  collectBankAccount: undefined,
  collector: undefined,
  collectPayments: undefined,
  collectHandlingFee: undefined,
  collectHandlingFl: undefined,
  collectAgeing: undefined,
  newTime: undefined,
  networkCalcStatus: undefined
})
// 用于存储初始值的变量
let previousFormData = {}

const fillData = async() => {
  const deptData = await DeptApi.getLoginUserDept()
  if (deptData.deptType === 1) {
    loginDept.value = deptData
  }
  const data = await WaybillApi.getWaybillByWaybillNo(waybillNo.value)
  formData.value = data
  formData.value.goodsList.forEach((item, index) => {
    item.goodsNo = index
  })
  await setOtherProperties()
  await computeFee()
  previousFormData = JSON.parse(JSON.stringify(formData.value))
}

const setOtherProperties = () => {
  formData.value.shipperName = formData.value.shipper.name
  formData.value.shipperMobile = formData.value.shipper.mobile
  formData.value.shipperAreaId = formData.value.shipper.areaId
  formData.value.shipperDetailAddress = formData.value.shipper.detailAddress
  formData.value.shipperIdCard = formData.value.shipper.idCard

  formData.value.consigneeName = formData.value.consignee.name
  formData.value.consigneeMobile = formData.value.consignee.mobile
  formData.value.consigneeAreaId = formData.value.consignee.areaId
  formData.value.consigneeDetailAddress = formData.value.consignee.detailAddress
}

const getValueAddedService = () => {
  const remark = formData.value.customerRemark || '';
  const keywords = ['易碎', '异型', '急发'];
  // 找出 remark 里包含的关键词
  const found = keywords.filter(word => remark.includes(word));
  if (found.length > 0) {
    return found.join('、');
  }
  // 没有关键词，优先显示 valueAddedService，再没有就用默认
  return  '';
}

watch(
  () => formData.value.endDeptId,
  (newVal) => {
    if (newVal !== undefined) {
      handleFreightScheme()
    }
  }
)
const routes = ref()
const handleFreightScheme = async () => {
  let startId = formData.value.startDeptId
  let endId = formData.value.endDeptId
  if (loginDept.value.branchDO.branchType === 12004) {
    startId = loginDept.value.branchDO.parentId
  }
  if (formData.value.endDeptBranchType === 12004) {
    endId = formData.value.endDeptParentId
  }
  await RouteApi.getRouteCheck(startId, endId).then(data => {
    if(data.length == 0) {
      routes.value = null
    } else {
      routes.value = data
    }
  })
  if (routes.value == null) {
    formData.value.endDeptId = undefined
    return
  }
  await fetchSteps(routes.value[0].id)
}

// 定义响应式数据
const steps = ref([])

// 模拟异步请求获取步骤信息
const fetchSteps = async (itemId: number) => {
  if (itemId !== undefined) {
    try {
      const response = await RouteApi.getRouteBranchListByRouteLineId(itemId);
      steps.value = response
        .map((item) => ({
          routeDeptName: item.routeDeptName,
          routeShortNum: item.routeShortNum
        })).sort((a, b) => a.routeShortNum - b.routeShortNum);
      if (loginDept.value.branchDO.branchType === 12004) {
        steps.value.unshift({ routeDeptName: formData.value.startDeptName, routeShortNum: 1 });
        steps.value.forEach((item, index) => item.routeShortNum = index + 1);
      }
      if (formData.value.endDeptBranchType === 12004) {
        const finalNumber = steps.value.length + 1;
        steps.value.push({ routeDeptName: formData.value.endDeptName, routeShortNum: finalNumber });
      } steps.value.sort((a, b) => a.routeShortNum - b.routeShortNum);
    } catch (error) {
      console.error('获取步骤信息失败:', error);
    }
  }
};


const trickList = ref<EventRouteVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  eventNo: waybillNo.value,
})


/** 查询列表 */
const getTrickList = async () => {
  queryParams.eventNo = waybillNo.value
  const data = await EventRouteApi.getEventRoutePage(queryParams)
  trickList.value = data.list
  total.value = data.total
}

//申请改单
const update = () => {
  //formRef.value.open(type, id)

  router.push({ path: '/wbms/update', query: { id: formData.value.id,waybillNo: formData.value.waybillNo, type: 'create' } })
}

const handlePrint = async () => {
  // 只打印运单标签
  let defaultPrinterName = ''
  const res = await PrintSettingApi.getPrintSettingInfo()
  if (res) {
    res.list.map((item) => {
      if ('label' === item.printerType) {
        defaultPrinterName = item.printerName
      }
    })
  }
  // 兼容不同页面的formData
  const waybillNoVal = formData.value.waybillNo || waybillNo.value
  const orderPrintContent = await WaybillApi.getWaybillPrintContent('10002', waybillNoVal)
  const LODOP = getLodop()
  // 获取打印机数量
  const printerCount = LODOP.GET_PRINTER_COUNT()
  let selectedPrinterIndex = -1
  for (let i = 0; i < printerCount; i++) {
    const printerName = LODOP.GET_PRINTER_NAME(i)
    if (printerName === defaultPrinterName) {
      selectedPrinterIndex = i
      break
    }
  }
  if (selectedPrinterIndex === -1) {
    message.error('未找到指定的打印机')
    return
  }
  LODOP.SET_PRINT_MODE('CATCH_PRINT_STATUS', !0)
  eval(orderPrintContent)
  LODOP.SET_PRINTER_INDEXA(defaultPrinterName)
  LODOP.PRINT()
  message.success('操作成功，正在打印中...')
}

//配载
const goToVoyage = () => {
  router.push({ path: '/tms/voyage' })
}

//删除申请
const deleteApplyFormRef = ref()

const openDeleteDialog = async() => {
  const queryParams = reactive({
    pageNo: 1,
    pageSize: 10,
    waybillNo: undefined,
    deleteApplyStatus: undefined,
    applyUserName: undefined,
    applyDeptId: undefined,
    createTime: []
  })
  queryParams.waybillNo = waybillNo.value
  queryParams.deleteApplyStatus  = 0
  const data = await DeleteApplyApi.getDeleteApplyPage(queryParams)
  if(data.total > 0) {
    message.warning("该运单已发起删除申请，请不要重复发起")
    return
  }
  // 传递运单号或ID，第二个参数为运单ID
  deleteApplyFormRef.value.open('create', undefined, waybillNo.value)
}

const onDeleteSuccess = () => {
  router.push({path:'/wbms/waybill'})
}

//转货
const showTransfer = computed(() => {
  return formData.value.waybillStatus === '8' &&
    formData.value.destination !== '' &&
    formData.value.destination !== undefined &&
    formData.value.isTransfer === true
})

const formRefWaybill = ref(false)
const transferFormRef = async() => {
  formRefWaybill.value.open('create', undefined, formData.value)
}

//送货
const delivery = () => {
  router.push({ path: '/exp/delivery-voyage' })
}

//签收
const formQSWaybill = ref(false)

const gotoQS = () => {
  formQSWaybill.value.open('create', formData.value.id)
}

//异常
const errorFormRefAdd = ref(false)
const gotoError = () => {
  errorFormRefAdd.value.open(waybillNo.value, true)
}

const receiptInfo = ref()

const receiptTransport = ref({})

const receiptTrackList = ref([])

// 其他tab页数据
const editDataList = ref([])
const errorList = ref([])
const financeList = ref([])
const 
const getReceipt = async () => {
  const receiptData = await ReceiptInfoApi.getReceiptInfoByWaybillCode(waybillNo.value)
  if (receiptData != null) {
    receiptInfo.value = receiptData
    receiptTransport.value = receiptData.receiptTransport
    receiptTrackList.value = receiptData.receiptTrackList
  }
}

// 获取改单记录
const getEditDataList = async () => {
  try {
    const data = await EditApi.getEditDetailListByWaybillNo(waybillNo.value)
    editDataList.value = data || []
  } catch (error) {
    console.error('获取改单记录失败:', error)
    editDataList.value = []
  }
}

// 获取异常记录
const getErrorList = async () => {
  try {
    const params = {
      pageNo: 1,
      pageSize: 100,
      waybillNo: waybillNo.value
    }
    const data = await OrderErrorApi.getOrderErrorPage(params)
    errorList.value = data.list || []
  } catch (error) {
    console.error('获取异常记录失败:', error)
    errorList.value = []
  }
}

// 获取财务记录
const getFinanceList = async () => {
  try {
    const data = await NetworkCalculateLogApi.getNetworkCalculateLogByWaybillCode(waybillNo.value)
    financeList.value = data || []
  } catch (error) {
    console.error('获取财务记录失败:', error)
    financeList.value = []
  }
}

const gotoBack = () => {
  router.go(-1)
}

const loading = ref(true)
onMounted( () => {
  waybillNo.value = route.query.waybillNo
   fillData()
   getTrickList()
   getReceipt()
   // 前置获取所有tab页数据，避免数据跳动
   getEditDataList()
   getErrorList()
   getFinanceList()
})
</script>

<style scoped>
/* 样式保持原样 */
.header-bar {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}
.header-bar .order-no {
  color: #f25643;
  margin-right: 16px;
  font-size: 14px;
  display: flex;
  align-items: center;
}
.header-bar .title {
  font-size: 20px;
  text-align-last: justify;
}
.header-bar .time {
  color: #909399;
  font-size: 14px;
}
.tab-bar {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  background: #fff;
  width: 100%;
}
.tab-list {
  display: flex;
  align-items: center;
  flex: 1;
}
.tab {
  position: relative;
  padding: 0 0 8px 0;
  display: flex;
  align-items: center;
  color: #606266;
  font-size: 15px;
  margin-right: 20px;
}
.tab.active {
  color: #409eff;
  font-weight: 500;
}
.tab.active::after {
  content: '';
  display: block;
  position: absolute;
  left: 50%;
  bottom: -2px;
  transform: translateX(-50%);
  width: 62px;
  height: 2px;
  background: #409eff;
  border-radius: 1px;
}
.tab-bar {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  background: #fff;
  width: 100%;
}
.tab-list {
  display: flex;
  align-items: center;
  flex: 1;
}
.custom-steps-bar {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
  min-width: 400px;
}
.custom-steps-flex {
  display: flex;
  align-items: center;
}
.custom-step-flex {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
}
.custom-step-label {
  color: #909399;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 4px;
  text-align: center;
  line-height: 1.2;
  padding: 0 8px;
}
.custom-step-label.active {
  color: #13c08a;
}
.custom-step-icon {
  font-size: 16px;
  color: #c0c4cc;
  margin-top: 4px;
}
.custom-step-icon.active {
  color: #13c08a;
}
.custom-step-line-flex {
  width: 50px;
  height: 3px;
  background: #ebeef5;
  margin: 0 4px 20px 0;
  flex: none;
}
.custom-step-line-flex.active {
  background: #13c08a;
    height: 1.5px;
}
.section-title-row {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}
.section-title {
  font-size: 16px;
  margin-bottom: 0;
  font-weight: normal;
}
.section-right {
  color: #909399;
  font-size: 14px;
  margin-left: 16px;
}
.fee-row {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}


.fee-divider {
  width: 100%;
  height: 1px;
  background: #ebeef5;
  margin: 8px 0 8px 0;
}
.fee-red {
  color:#f25643;
  margin-right: 8px;
}
.table-section {
  margin: 8px 0 12px 0;
}
.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 0;
  align-items: flex-start;
  flex-wrap: wrap;
}
.block-form {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 14px;
  margin-bottom: 16px;
  background: #fff;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
}
.block-form .el-form-item {
  min-width: 220px;
  margin-bottom: 8px;
}
.block-form .el-form-item:last-of-type {
  margin-bottom: 0 !important;
}


.block-form .section-title,
.block-form .section-title-row {
  margin-bottom: 8px;
}
.button-group-form {
  margin-bottom: 16px;
  background: transparent;
  display: flex;
  justify-content: flex-end;
}
.button-group-right {
  display: flex;
  gap: 12px;
}
.action-btn {
  border-radius: 6px;
  background: #fff;
  border: 1px solid #dcdfe6;
  color: #303133;
  font-weight: 400;
  min-width: 60px;
  padding: 0 18px;
}
.action-btn.el-button--primary {
  background: #409eff;
  color: #fff;
  border: 1px solid #409eff;
}

/* 修复备注表单滚动条问题 */
.remark-form {
  overflow: visible !important; /* 防止出现不必要的滚动条 */
  height: auto !important; /* 让高度自适应内容 */
}

</style>
