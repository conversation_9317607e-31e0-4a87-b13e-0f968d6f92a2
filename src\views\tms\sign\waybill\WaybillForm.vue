<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" >
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" v-loading="formLoading" >
      <el-form-item label="运单号" prop="waybillNo">
        <el-input v-model="formData.waybillNo" />
      </el-form-item>
      <el-form-item label="提付金额" prop="tifuYf">
        <el-input v-model="formData.tifuYf" />
      </el-form-item>
      <el-form-item label="代收款" prop="collectPayments">
        <el-input v-model="formData.collectPayments" />
      </el-form-item>
      <!-- <el-form-item label="应收金额" prop="waybillNo">
        <el-input v-model="formData.waybillNo" />
      </el-form-item> -->
      <el-form-item label="发货人" prop="shipper">
        <el-input v-model="formData.shipper.name" />
      </el-form-item>
      <el-form-item label="货物名称" prop="goodsList">
        <el-input v-model="formData.goodsList[0].goodsName" />
      </el-form-item>
      <el-form-item label="回单数量" prop="receiptNum">
        <el-input v-model="formData.receiptNum" />
      </el-form-item>
      <el-form-item label="发货电话" prop="shipper">
        <el-input v-model="formData.shipper.mobile" />
      </el-form-item>
      <el-form-item label="包装" prop="goodsList">
        <el-input v-model="formData.goodsList[0].goodsPackageUnit" />
      </el-form-item>
      <el-form-item label="回单类型" prop="receiptType">
        <el-select v-model="formData.receiptType">
          <el-option v-for="dict in getStrDictOptions(DICT_TYPE.RECEIPT_TYPE)" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="收货人" prop="consignee">
        <el-input v-model="formData.consignee.name" />
      </el-form-item>
      <el-form-item label="总件数" prop="goodsList">
        <el-input v-model="formData.goodsList[0].goodsNum" />
      </el-form-item>
      <el-form-item label="收货电话" prop="consignee">
        <el-input v-model="formData.consignee.mobile" />
      </el-form-item>
      <el-form-item label="结算方式" prop="consigneeId">
        <el-input v-model="formData.consigneeId" />
      </el-form-item>
      <el-form-item label="仓储费" prop="consigneeId">
        <el-input v-model="formData.consigneeId" />
      </el-form-item>
<!--      <el-form-item label="费用表id" prop="expenseId">-->
<!--        <el-input v-model="formData.expenseId" />-->
<!--      </el-form-item>-->

      <el-form-item label="总费用" prop="totalCost">
        <el-input v-model="formData.totalCost" />
      </el-form-item>

      <el-form-item label="关联单号" prop="orderRefNo">
        <el-input v-model="formData.orderRefNo" />
      </el-form-item>

      <el-form-item label="收货人" prop="consignee">
        <el-input v-model="formData.consignee.name" />
      </el-form-item>
      <el-form-item label="收货人身份证号" prop="consignee">
        <el-input v-model="formData.consignee.idCard" />
      </el-form-item>
      <el-form-item label="代收人" prop="collector">
        <el-input v-model="formData.collector" placeholder="请输入代收人" />
      </el-form-item>
      <el-form-item label="代收银行名称" prop="collectBankName">
        <el-input v-model="formData.collectBankName" placeholder="请输入代收银行名称" />
      </el-form-item>
      <el-form-item label="代收银行账户" prop="collectBankAccount">
        <el-input v-model="formData.collectBankAccount" placeholder="请输入代收银行账户" />
      </el-form-item>
      <el-form-item label="代收手续费" prop="collectHandlingFee">
        <el-input v-model="formData.collectHandlingFee" placeholder="请输入代收手续费" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { WaybillApi, WaybillVO } from '@/api/wbms/waybill'
import { getUserProfile } from '@/api/system/user/profile'
import { EventRouteApi } from '@/api/wbms/eventroute'
import { onMounted, ref, reactive, watch } from 'vue'
import { ReceiptInfoVO, ReceiptInfoApi, ReceiptTrackApi, ReceiptTransportApi } from '@/api/wbms/receipt'

/** 运单 表单 */
defineOptions({ name: 'WaybillForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  flag: '1',
  id: undefined,
  waybillNo: undefined,
  orderId: undefined,
  waybillStatus: undefined,
  transportType: undefined,
  shipperId: undefined,
  consigneeId: undefined,
  pickUpType: 0,
  isDelivery: false,
  deliveryType: undefined,
  expenseId: undefined,
  takeDelivery: false,
  waitReleaseGoods: false,
  existReceipt: false,
  receiptType: undefined,
  receiptNum: undefined,
  positionId: undefined,
  shipperPay: undefined,
  consigneePay: undefined,
  totalCost: undefined,
  orderRefNo: undefined,
  existPicture: undefined,
  existRemark: undefined,
  remark: undefined,
  deptId: undefined,
  userId: undefined,
  startDeptId: undefined,
  endDeptId: undefined,
  destination: undefined,

  goodsList: [
    {
      goodsNo: 0,
      goodsName: undefined,
      goodsPackageUnit: undefined,
      goodsNum: undefined,
      goodsVolume: undefined,
      goodsWeight: undefined,
      goodsPrice: undefined
    }
  ],
  shipper: {
    name: undefined,
    mobile: undefined,
    areaId: undefined,
    detailAddress: undefined,
    idCard: undefined
  },
  consignee: {
    name: undefined,
    mobile: undefined,
    areaId: undefined,
    detailAddress: undefined,
    idCard: undefined
  },
  shipperName: undefined,
  shipperMobile: undefined,
  shipperAreaId: undefined,
  shipperDetailAddress: undefined,
  shipperIdCard: undefined,
  consigneeName: undefined,
  consigneeMobile: undefined,
  consigneeAreaId: undefined,
  consigneeDetailAddress: undefined,
  biaozhunYf: undefined,
  zhekouYf: undefined,
  zhehouYf: undefined,
  huidanfuYf: undefined,
  koufuYf: undefined,
  tifuYf: undefined,
  tifuyuejieYf: undefined,
  xianfuYf: undefined,
  xianfuyuejieYf: undefined,
  xiayouYf: undefined,
  xiayouYfPt: undefined,
  baoxianfei: undefined,
  baoxianfeiFl: undefined,
  baozhifei: undefined,
  baozhifeiPt: undefined,
  dianfufei: undefined,
  dianfufeiPt: undefined,
  jiehuofei: undefined,
  jiehuofeiPt: undefined,
  songhuofei: undefined,
  songhuofeiPt: undefined,
  zhidanfei: undefined,
  zhidanfeiPt: undefined,
  huidanfei: undefined,
  huidanfeiPt: undefined,
  konghuofei: undefined,
  konghuofeiPt: undefined,
  waizhuanfei: undefined,
  waizhuanfeiPt: undefined,
  yongjin: undefined,
  yongjinPt: undefined,
  yongjinSkr: undefined,
  yongjinAccount: undefined,
  yongjinMobile: undefined,
  other1: undefined,
  other1Pt: undefined,
  other2: undefined,
  other2Pt: undefined,
  other3: undefined,
  other3Pt: undefined,
  other4: undefined,
  other4Pt: undefined,
  other5: undefined,
  other5Pt: undefined,
  other6: undefined,
  other6Pt: undefined,
  other7: undefined,
  other7Pt: undefined,
  other8: undefined,
  other8Pt: undefined,
  other9: undefined,
  other9Pt: undefined,
  collectBankName: undefined,
  collectBankAccount: undefined,
  collector: undefined,
  collectPayments: undefined,
  collectHandlingFee: undefined,
  collectAgeing: undefined
})

const loginUserProfile = ref()

const formRules = reactive({
  // waybillNo: [{ required: true, message: '运单号不能为空', trigger: 'blur' }],
  // orderId: [{ required: true, message: '订单id不能为空', trigger: 'blur' }],
  // shipperId: [{ required: true, message: '发货人id不能为空', trigger: 'blur' }],
  // consigneeId: [{ required: true, message: '收货人id不能为空', trigger: 'blur' }],
  // pickUpType: [{ required: true, message: '取件类型不能为空', trigger: 'change' }],
  // deliveryType: [{ required: true, message: '送货类型不能为空', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await WaybillApi.getWaybill(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    setOtherProperties()
    const data = formData.value as unknown as WaybillVO
    if (formType.value === 'create') {
      await WaybillApi.createWaybill(data)
      message.success(t('common.createSuccess'))
    } else {
      if (data.waybillStatus == '6') {
        data.receiptStatus = '21'
        // data.signTime = new Date()
        // await WaybillApi.updateWaybillStatus(data)
        // message.success(t('common.updateSuccess'))

        // //生成回单基本信息
        await ReceiptInfoUpdate(data.waybillNo, data.waybillStatus)
        await ReceiptTrackCreate(data.waybillNo, data.waybillStatus)
      } else {
        if (data.deliveryType == '1') {
          message.warning("请前往派送后签收！")
          return
        }
        if (data.existReceipt) // 是否要回单 \回单类型)
        {
          //生成回单基本信息
          await ReceiptInfoUpdate(data.waybillNo, data.waybillStatus)
          //记录回单生成状态
          await ReceiptTrackCreate(data.waybillNo, data.waybillStatus)
          if (data.receiptType == '2') {
            await ReceiptTransportCreate(data)
          }
        }
        data.waybillStatus = '6'
        data.signTime = new Date()
        await handleSave(data.waybillNo)
      }
      await WaybillApi.updateWaybillStatus(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

// 保存按钮的点击事件处理函数
const handleSave = async (waybillNo: string) => {
  const eventroute = {
    deptId: loginUserProfile.value.dept.id,
    deptName: loginUserProfile.value.dept.name,
    eventNo: waybillNo,
    // 货物已由【李四】签收
    eventLog: `货物已由[${formData.value.consignee.name ?? ''}]签收`,
    eventNode: '16118',
    eventNodeValue: '客户签收',
    eventType: '16201',
    userCode: loginUserProfile.value.id,
    userName: loginUserProfile.value.username
  }
  if (formData.value.collector != undefined) {
    eventroute.eventLog = `货物已由[${formData.value.collector ?? ''}]签收`
  }

  await EventRouteApi.createEventRoute(eventroute)
}

//生成回单基本信息

const ReceiptInfoUpdate = async (waybillCode: string, waybillStatus: string) => {
  const receiptInfo = ref()
  if (waybillStatus == '6') {
    receiptInfo.value = {
      waybillCode: waybillCode,//运单号
      // receiptType: waybillData.value.receiptType,//回单类型
      receiptStatus: '21',//回单状态
      // signTime: new Date().toISOString(),//签署时间
      // receiptSign: formData.value.consignee.name, //签署人
      // signPhone: formData.value.consignee.mobile,//签署人电话
      // receiptWitness: voyage.value?.driverName1,//见证人
      // witnessPhone: voyage.value?.contactMobile1//见证人电话
      receiptReceive: formData.value.shipper.name,//领取人
      receiveTime: new Date().getTime(),//领取时间
      receivePhone: formData.value.shipper.mobile//领取人电话
    }

  } else {
    receiptInfo.value = {
      waybillCode: waybillCode,//运单号
      receiptStatus: '17',//回单状态
      signTime: new Date().getTime(),//签署时间
      receiptSign: formData.value.consignee.name, //签署人
      signPhone: formData.value.consignee.mobile,//签署人电话
    }

    if (formData.value.collector != undefined) {
      receiptInfo.value.receiptSign = formData.value.collector
    }

  }
  await ReceiptInfoApi.updateReceiptInfo(receiptInfo.value)
}

//记录回单生成状态
const ReceiptTrackCreate = async (waybillNo: string, waybillStatus: string) => {
  const receipt = await ReceiptInfoApi.getReceiptInfoByWaybillCode(waybillNo)
  const receiptTrack = ref()
  if (waybillStatus == '6') {
    receiptTrack.value = {
      receiptCode: receipt.receiptCode,
      nodeType: '4',
      nodeName: '回单领取',
      nodeTime: new Date().getTime(),
      nodeContent: `回单已由[${formData.value.shipper.name}]领取`,
      opUserName: loginUserProfile.value.username
    }
  } else {
    receiptTrack.value = {
      receiptCode: receipt.receiptCode,
      nodeType: '0',
      nodeName: '回单生成',
      nodeTime: new Date().getTime(),
      nodeContent: `客户[${formData.value.consignee.name}]签收，回单生成`,
      opUserName: loginUserProfile.value.username
    }
    if (formData.value.collector != undefined) {
      receiptTrack.value.nodeContent = `客户[${formData.value.collector}]签收，回单生成`
    }
  }

  await ReceiptTrackApi.createReceiptTrack(receiptTrack.value)
}

//记录回单运输信息
const ReceiptTransportCreate = async (waybill) => {
  const receipt = await ReceiptInfoApi.getReceiptInfoByWaybillCode(waybill.waybillNo)
  const receiptTransport = {
    receiptCode: receipt.receiptCode
    // expressCompany: '百达物流',
    // expressCode: waybill.waybillNo,
    // receiptSend: waybill.consignee.name,
    // sendTime: new Date().getTime(),
    // sendPhone: waybill.consignee.mobile,
    // sendAddress: waybill.consignee.detailAddress,
    // receiptRecipient: waybill.shipper.name,
    // recipientTime: ,
    // recipientPhone: waybill.shipper.mobile,
    // recipientAddress: waybill.shipper.detailAddress
  }

  await ReceiptTransportApi.createReceiptTransport(receiptTransport)
}

const setOtherProperties = () => {
  formData.value.shipperName = formData.value.shipper.name
  formData.value.shipperMobile = formData.value.shipper.mobile
  formData.value.shipperAreaId = formData.value.shipper.areaId
  formData.value.shipperDetailAddress = formData.value.shipper.detailAddress

  formData.value.consigneeName = formData.value.consignee.name
  formData.value.consigneeMobile = formData.value.consignee.mobile
  formData.value.consigneeAreaId = formData.value.consignee.areaId
  formData.value.consigneeDetailAddress = formData.value.consignee.detailAddress
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    waybillNo: undefined,
    orderId: undefined,
    waybillStatus: undefined,
    transportType: undefined,
    shipperId: undefined,
    consigneeId: undefined,
    pickUpType: 0,
    isDelivery: false,
    deliveryType: undefined,
    expenseId: undefined,
    takeDelivery: false,
    waitReleaseGoods: false,
    existReceipt: false,
    receiptType: undefined,
    receiptNum: undefined,
    positionId: undefined,
    shipperPay: undefined,
    consigneePay: undefined,
    totalCost: undefined,
    orderRefNo: undefined,
    existPicture: undefined,
    existRemark: undefined,
    remark: undefined,
    deptId: undefined,
    userId: undefined,
    startDeptId: undefined,
    endDeptId: undefined,
    destination: undefined,
    goodsList: [
      {
        goodsNo: 0,
        goodsName: undefined,
        goodsPackageUnit: undefined,
        goodsNum: undefined,
        goodsVolume: undefined,
        goodsWeight: undefined,
        goodsPrice: undefined
      }
    ],
    shipper: {
      name: undefined,
      mobile: undefined,
      areaId: undefined,
      detailAddress: undefined,
      idCard: undefined
    },
    consignee: {
      name: undefined,
      mobile: undefined,
      areaId: undefined,
      detailAddress: undefined,
      idCard: undefined
    },
    shipperName: undefined,
    shipperMobile: undefined,
    shipperAreaId: undefined,
    shipperDetailAddress: undefined,
    shipperIdCard: undefined,
    consigneeName: undefined,
    consigneeMobile: undefined,
    consigneeAreaId: undefined,
    consigneeDetailAddress: undefined,
    biaozhunYf: undefined,
    zhekouYf: undefined,
    zhehouYf: undefined,
    huidanfuYf: undefined,
    koufuYf: undefined,
    tifuYf: undefined,
    tifuyuejieYf: undefined,
    xianfuYf: undefined,
    xianfuyuejieYf: undefined,
    xiayouYf: undefined,
    xiayouYfPt: undefined,
    baoxianfei: undefined,
    baoxianfeiFl: undefined,
    baozhifei: undefined,
    baozhifeiPt: undefined,
    dianfufei: undefined,
    dianfufeiPt: undefined,
    jiehuofei: undefined,
    jiehuofeiPt: undefined,
    songhuofei: undefined,
    songhuofeiPt: undefined,
    zhidanfei: undefined,
    zhidanfeiPt: undefined,
    huidanfei: undefined,
    huidanfeiPt: undefined,
    konghuofei: undefined,
    konghuofeiPt: undefined,
    waizhuanfei: undefined,
    waizhuanfeiPt: undefined,
    yongjin: undefined,
    yongjinPt: undefined,
    yongjinSkr: undefined,
    yongjinAccount: undefined,
    yongjinMobile: undefined,
    other1: undefined,
    other1Pt: undefined,
    other2: undefined,
    other2Pt: undefined,
    other3: undefined,
    other3Pt: undefined,
    other4: undefined,
    other4Pt: undefined,
    other5: undefined,
    other5Pt: undefined,
    other6: undefined,
    other6Pt: undefined,
    other7: undefined,
    other7Pt: undefined,
    other8: undefined,
    other8Pt: undefined,
    other9: undefined,
    other9Pt: undefined,
    collectBankName: undefined,
    collectBankAccount: undefined,
    collector: undefined,
    collectPayments: undefined,
    collectHandlingFee: undefined,
    collectAgeing: undefined
  }
  formRef.value?.resetFields()
}

/** 初始化 */
onMounted(async () => {
  loginUserProfile.value = await getUserProfile()
})
</script>

<style scoped>

</style>
