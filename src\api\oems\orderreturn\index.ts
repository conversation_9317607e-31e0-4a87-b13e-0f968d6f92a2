import request from '@/config/axios'

// 退货 VO
export interface OrderReturnVO {
  id: number // 编号
  returnNo: string // 退货编号
  returnStatus: number // 退货状态
  waybillId: number // 运单号
  waybillNo: string // 运单编号
  applyBranchId: number // 申请站点ID
  applyTime: Date // 申请时间
  applyBranchName: string // 申请站点名
  applyReason: string // 退货原因
  returnBranchId: number // 退货站点ID
  returnBranchName: string // 退货站点名
  auditBranchId: number // 审批站点ID
  auditBranchName: string // 审批站点名
  auditTime: Date // 审批退货时间
  returnApplyReason: string // 反退货申请信息
  returnRejectReason: string // 反退货驳回备注
  rejectReason: string // 驳回原因
  remark: string // 备注
}

// 退货 API
export const OrderReturnApi = {
  // 查询退货分页
  getOrderReturnPage: async (params: any) => {
    return await request.get({ url: `/oems/order-return/page`, params })
  },

  // 查询退货详情
  getOrderReturn: async (id: number) => {
    return await request.get({ url: `/oems/order-return/get?id=` + id })
  },

  // 新增退货
  createOrderReturn: async (data: OrderReturnVO) => {
    return await request.post({ url: `/oems/order-return/create`, data })
  },

  // 修改退货
  updateOrderReturn: async (data: OrderReturnVO) => {
    return await request.put({ url: `/oems/order-return/update`, data })
  },

  // 删除退货
  deleteOrderReturn: async (id: number) => {
    return await request.delete({ url: `/oems/order-return/delete?id=` + id })
  },

  // 导出退货 Excel
  exportOrderReturn: async (params) => {
    return await request.download({ url: `/oems/order-return/export-excel`, params })
  },
}
