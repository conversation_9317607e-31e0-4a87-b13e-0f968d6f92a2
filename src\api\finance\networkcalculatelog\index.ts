import request from '@/config/axios'

// 网点清分记录 VO
export interface NetworkCalculateLogVO {
  id: number // 自增序列
  logisticsNetworkId: number // 网点id
  orderCode: string // 订单号
  waybillCode: string // 运单号
  networkFreightPreValue: number // 网点运费预付账户增加金额
  networkFreightDivideValue: number // 网点运费分成账户增加金额
  networkTaxValue: number // 网点税费账户增加金额
  networkBrokerageValue: number // 网点佣金账户增加金额
  platformFreightValue: number // 平台运费账户增加金额
  platformProfitValue: number // 平台毛利账户增加金额
  platformBrokerageValue: number // 平台佣金账户增加金额
  calcTime: Date // 清分时间
  collectName: string // 收货人姓名
  collectPhone: string // 收货人手机号
  collectAddress: string // 收货人地址
  sendName: string // 寄件人姓名
  sendPhone: string // 寄件人手机号
  sendIdCard: string // 寄件人身份证号
  sendAddress: string // 寄件人地址
  goodsType: string // 物品类型（数据字典）
  goodsTypeLabel: string // 物品名称
  totalWeight: number // 总重量（Kg）
  totalVolume: number // 总体积（m³）
  totalNum: number // 总件数（件）
  goodsLong: number // 物品长（cm）
  goodsWidth: number // 物品宽（cm）
  goodsHeight: number // 物品高（cm）
  payMethod: string // 付款方式（数据字典）
  shouldPayAmount: number // 应付金额 弃用
  actualPayAmount: number // 实付金额
  isReceipt: string // 是否回单：0 否 1 是
  receiptType: string // 回单类型（1-电子回单 2-纸质回单）
  receiptAmount: number // 回单费
  deliveryMethod: string // 送货方式：1 自提 2 送货
  releaseMethod: string // 放货方式：1 等通知 2 其他
  releaseAmount: number // 控货费
  packMethod: string // 包装方式：1 箱 2 纸 3 袋 4 桶 5 木 6 膜 7 皮 8 布
  collectionDelivery: number // 代收货款 如果是0，则表示不需要代收货款
  collectionName: string // 代收款人
  collectionBankName: string // 收款行
  collectionCardNum: string // 收款卡号
  deliveryAmount: number // 送货费用
  insuredAmount: number // 保价费：默认保费利率是千分之一
  insuredPayAmount: number // 支付的保价费
  amountFreight: number // 运费金额
  totalAmount: number // 总金额
  brokerageAmount: number // 佣金
  brokerageBackType: string // 佣金返款方式（1-现返 2-欠返）
  otherAmount: number // 其他费用
  discountAmount: number // 优惠金额
  collectionFreightAmount: number // 代收运费
  collectionPayAmount: number // 代收手续费
  amountZdf: number // 制单费
  rateBzf: number // 保值费率(‰)
  collectionProcRate: number // 代收款手续费率(‰)
  remark: string // 备注
}

// 网点清分记录 API
export const NetworkCalculateLogApi = {
  // 查询网点清分记录分页
  getNetworkCalculateLogPage: async (params: any) => {
    return await request.get({ url: `/wljh/network-calculate-log/page`, params })
  },

  // 查询网点清分记录详情
  getNetworkCalculateLog: async (id: number) => {
    return await request.get({ url: `/wljh/network-calculate-log/get?id=` + id })
  },

  // 新增网点清分记录
  createNetworkCalculateLog: async (data: NetworkCalculateLogVO) => {
    return await request.post({ url: `/wljh/network-calculate-log/create`, data })
  },

  // 修改网点清分记录
  updateNetworkCalculateLog: async (data: any) => {
    return await request.put({ url: `/wljh/network-calculate-log/update`, data })
  },

  // 删除网点清分记录
  deleteNetworkCalculateLog: async (id: number) => {
    return await request.delete({ url: `/wljh/network-calculate-log/delete?id=` + id })
  },

  // 导出网点清分记录 Excel
  exportNetworkCalculateLog: async (params) => {
    return await request.download({ url: `/wljh/network-calculate-log/export-excel`, params })
  },

  // 结算管理-查询网点清分记录
  getNetworkSettlePage: async (params: any) => {
    return await request.get({ url: `/wljh/network-calculate-log/pageSettle`, params })
  },
  // 结算管理-导出网点清分记录 Excel
  exportNetworkSettle: async (params) => {
    return await request.download({ url: `/wljh/network-calculate-log/export-excel-settle`, params })
  },
  // 批量结算
  settle: async (data: any) => {
    return await request.post({ url: `/wljh/network-calculate-log/settle`, data })
  },
  // 批量审核
  audit: async (data: any) => {
    return await request.post({ url: `/wljh/network-calculate-log/audit`, data })
  },
  // 批量归档
  archive: async (data: any) => {
    return await request.post({ url: `/wljh/network-calculate-log/archive`, data })
  },
  // 批量划转
  transfer: async (data: any) => {
    return await request.post({ url: `/wljh/network-calculate-log/transfer`, data })
  },

  //根据运单获取清分与划转记录
  getNetworkCalculateLogByWaybillCode: async (waybillNo: string) => {
    return await request.get({ url: `/wljh/network-calculate-log/getTransferLogByWaybill?waybillNo=` + waybillNo })
  }    
}
