<template>
  <!-- 顶部栏，紧贴卡片 -->
  <div class="header-bar">
    <div class="order-no">
      <span>理赔编号：{{ formData.id }}</span>
      <el-icon style="margin-left: 8px;color:#606266;cursor:pointer" @click="copyClaimId"><CopyDocument /></el-icon>
    </div>
    <div class="title">理 赔 详 情</div>
    <div class="time">申请时间：{{ formatDate(formData.applyTime) }}</div>
  </div>
    <el-card style="border: none;box-shadow: none">
      <!-- tab栏 -->
      <div class="tab-bar">
        <div class="tab-list">
          <div
            v-for="(tab, i) in tabList"
            :key="tab.text" style="cursor: pointer"
            :class="['tab', {active: i === activeTabIndex}]"
            @click="activeTabIndex = i"
            v-show="shouldShowTab(i)"
          >
            {{tab.text}}
          </div>
        </div>
        <div class="custom-steps-bar">
          <div class="custom-steps-flex">
            <template v-for="(step, i) in stepList" :key="step.text">
              <div class="custom-step-flex">
                <div :class="['custom-step-label', { active: step.done }]">{{ step.text }}</div>
                <el-icon :class="['custom-step-icon', {active: step.done}]">
                  <CircleCheck v-if="step.done" />
                </el-icon>
              </div>
              <div
                v-if="i < stepList.length - 1"
         :class="['custom-step-line-flex', { active: stepList[i + 1].done }]"
              ></div>
            </template>
          </div>
        </div>
      </div>

      <!-- 理赔详情 -->
      <div v-if="activeTabIndex === 0">
        <!-- 基础信息 -->
        <el-form class="block-form" label-width="100px" label-position="right" inline label-suffix=":">
          <div class="section-title-row">
            <span class="section-title">理赔基础信息</span>
            <span class="section-right">
              <dict-tag :type="DICT_TYPE.OEMS_CLAIM_STATUS" :value="formData.claimStatus" />
            </span>
          </div>
          <el-form-item label="运单编号">
            <el-link @click="handleWaybillNoClick(formData.waybillNo)" class="form-value">
              {{ formData.waybillNo }}
            </el-link>
          </el-form-item>
          <el-form-item label="理赔类型">
            <dict-tag :type="DICT_TYPE.OEMS_CLAIM_TYPE" :value="formData.claimType" />
          </el-form-item>
          <el-form-item label="理赔金额"><span class="form-value">{{ formData.applyAmount }}元</span></el-form-item>
          <el-form-item label="申请时间"><span class="form-value">{{ formatDate(formData.applyTime) }}</span></el-form-item>
          <el-form-item label="申请原因" style="width: 100%;">
            <span class="form-value">{{ formData.applyReason || '无' }}</span>
          </el-form-item>
        </el-form>

      </div>

      <!-- 审批信息 -->
      <div v-else-if="activeTabIndex === 1">
        <el-table
          :data="approvalRecords"
          style="width: 100%;margin-bottom: 20px;"
          :header-cell-style="{ color: '#606266', textAlign: 'center', fontWeight: 400 }"
          :cell-style="{ textAlign: 'center' }"
          v-loading="recordsLoading"
        >
          <el-table-column prop="approvalName" label="审批人" />
          <el-table-column prop="approvalTime" label="审批时间" width="180">
            <template #default="scope">
              {{ formatDate(scope.row.approvalTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="审批备注" min-width="200" />
          <el-table-column prop="claimStatus" label="是否通过" width="120">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.OEMS_CLAIM_STATUS" :value="scope.row.claimStatus" />
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 处理记录 -->
      <div v-else-if="activeTabIndex === 2 && formData.claimStatus >= 3">
        <!-- 沟通记录 -->
        <el-form class="block-form" inline label-suffix=":">
          <div class="section-title" style="width: 100%;">沟通记录</div>
          <div class="table-section" style="width: 100%;">
            <el-table
              :data="communicationRecords"
        
              style="width: 100%;"
          :header-cell-style="{ color: '#606266', textAlign: 'center', fontWeight: 400 }"
          :cell-style="{ textAlign: 'center' }"
              v-loading="recordsLoading"
            >
              <el-table-column prop="replyBranchName" label="回复站点名" />
              <el-table-column prop="replyContent" label="回复内容" min-width="200" />
              <el-table-column prop="replyTime" label="回复时间" width="180">
                <template #default="scope">
                  {{ formatDate(scope.row.replyTime) }}
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注" min-width="150" />
            </el-table>
          </div>
        </el-form>

        <!-- 理赔定责 -->
        <el-form class="block-form" inline label-suffix=":">
          <div class="section-title" style="width: 100%;">理赔定责</div>
          <div class="table-section" style="width: 100%;">
            <el-table
              :data="liabilityRecords"
              style="width: 100%;"
              :header-cell-style="{ color: '#606266', textAlign: 'center', fontWeight: 400 }"
              :cell-style="{ textAlign: 'center' }"
              v-loading="recordsLoading"
            >
              <el-table-column prop="responsibleType" label="责任人类型" width="120">
                <template #default="scope">
                  <dict-tag :type="DICT_TYPE.OEMS_PERSON_LIABLE_TYPE" :value="scope.row.responsibleType" />
                </template>
              </el-table-column>
              <el-table-column prop="liabilityType" label="责任类型" width="120">
                <template #default="scope">
                  <dict-tag :type="DICT_TYPE.OEMS_LIABLE_TYPE" :value="scope.row.liabilityType" />
                </template>
              </el-table-column>
              <el-table-column prop="penaltyAmount" label="处罚金额" width="100" />
              <el-table-column prop="liabilityAmount" label="定责金额" width="100" />
              <el-table-column prop="responsibleName" label="责任人名称" />
              <el-table-column prop="branchName" label="站点名称" />
              <el-table-column prop="mobile" label="手机号码" />
              <el-table-column prop="remark" label="备注" min-width="150" />
            </el-table>
          </div>
        </el-form>

        <!-- 理赔付款 -->
        <el-form class="block-form" label-width="100px" label-position="right" inline label-suffix=":">
          <div class="section-title-row">
            <span class="section-title">理赔付款</span>
          </div>
          <el-form-item label="理赔付款人"><span class="form-value">{{ formData.payer || '暂无' }}</span></el-form-item>
          <el-form-item label="赔付金额"><span class="form-value">{{ formData.payAmount || '暂无' }}元</span></el-form-item>
          <el-form-item label="付款方式">
            <dict-tag :type="DICT_TYPE.OEMS_CLAIM_PAY_TYPE" :value="formData.payType" />
          </el-form-item>
          <el-form-item label="赔付时间"><span class="form-value">{{ formatDate(formData.payTime) || '暂无' }}</span></el-form-item>
          <el-form-item label="理赔收款人"><span class="form-value">{{ formData.collectPay.name || '暂无' }}</span></el-form-item>
          <el-form-item label="收款人手机"><span class="form-value">{{ formData.collectPay.mobile || '暂无' }}</span></el-form-item>
          <el-form-item label="银行名称"><span class="form-value">{{ formData.collectPay.bankName || '暂无' }}</span></el-form-item>
          <el-form-item label="银行卡号"><span class="form-value">{{ formData.collectPay.bankAccount || '暂无' }}</span></el-form-item>
          <el-form-item label="身份证号码"><span class="form-value">{{ formData.collectPay.idCard || '暂无' }}</span></el-form-item>
          <el-form-item label="付款备注" style="width: 100%;">
            <span class="form-value">{{ formData.payReason || '无' }}</span>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮 -->
      <div class="button-group-form">
        <div class="button-group-right">
          <el-button @click="goBack" class="action-btn">返回</el-button>
          <el-button 
            v-if="formData.claimStatus === 0" 
            type="primary" 
            class="action-btn"
            @click="handleModifyApply"
          >
            修改申请
          </el-button>
          <el-button 
            v-if="formData.claimStatus === 0" 
            type="primary" 
            class="action-btn"
            @click="handleRevoke"
          >
            理赔撤销
          </el-button>
          <el-button 
            v-if="formData.claimStatus === 0" 
            type="primary" 
            class="action-btn"
            @click="handleApproval"
          >
            理赔审批
          </el-button>
          <el-button
            v-if="formData.claimStatus === 3 || formData.claimStatus === 4"
            type="primary"
            class="action-btn"
            @click="handleProcess"
          >
            理赔处理
          </el-button>
          <el-button
            v-if="formData.claimStatus === 5"
            type="primary"
            class="action-btn"
            @click="handlePayment"
          >
            理赔付款
          </el-button>
        </div>
      </div>
    </el-card>

  <!-- 理赔表单弹窗 -->
  <OrderClaimForm ref="claimFormRef" @success="handleFormSuccess" />
  <!-- 理赔处理弹窗 -->
  <OrderClaimDisposeForm ref="disposeFormRef" @success="handleFormSuccess" />
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CopyDocument, CircleCheck } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/formatTime'
import { OrderClaimApi } from '@/api/oems/orderclaim'
import { DICT_TYPE } from '@/utils/dict'
import OrderClaimForm from './OrderClaimForm.vue'
import OrderClaimDisposeForm from './OrderClaimDisposeForm.vue'

defineOptions({ name: 'OrderClaimDetail' })

const route = useRoute()
const router = useRouter()

// 响应式数据
const formData = ref<any>({})
const approvalRecords = ref<any[]>([])
const communicationRecords = ref<any[]>([])
const liabilityRecords = ref<any[]>([])
const loading = ref(false)
const recordsLoading = ref(false)
const activeTabIndex = ref(0)

// Tab列表
const tabList = [
  { text: '理赔详情' },
  { text: '审批信息' },
  { text: '处理记录' }
]

// 控制tab显示的函数
const shouldShowTab = (tabIndex: number) => {
  switch (tabIndex) {
    case 0: // 理赔详情
      return true
    case 1: // 审批信息
      return formData.value.claimStatus > 1
    case 2: // 处理记录
      return formData.value.claimStatus > 2
    default:
      return true
  }
}

// 检查并调整当前选中的tab
const adjustActiveTab = () => {
  if (!shouldShowTab(activeTabIndex.value)) {
    // 如果当前tab不可见，切换到第一个可见的tab
    for (let i = 0; i < tabList.length; i++) {
      if (shouldShowTab(i)) {
        activeTabIndex.value = i
        break
      }
    }
  }
}

// 状态步骤
const stepList = computed(() => {
  const status = formData.value.claimStatus
  const steps = [
    { text: '已申请', done: status >= 0 },
    { text: '已审批', done: status >= 3 },
    { text: '处理中', done: status >= 4 },
    { text: '已处理', done: status >= 5 },
    { text: '已结束', done: status === 6 }
  ]

  // 如果是撤销或驳回状态，显示不同的步骤
  if (status === 1) {
    return [
      { text: '已申请', done: true },
      { text: '已撤销', done: true }
    ]
  } else if (status === 2) {
    return [
      { text: '已申请', done: true },
      { text: '已驳回', done: true }
    ]
  }

  return steps
})

// 获取理赔详情
const getClaimDetail = async () => {
  const id = route.query.id as string
  if (!id) {
    ElMessage.error('缺少理赔ID参数')
    return
  }

  try {
    loading.value = true
    formData.value = await OrderClaimApi.getOrderClaim(Number(id))
    // 获取理赔详情后，调整tab显示
    adjustActiveTab()
    // 获取理赔详情后，更新记录数据
    await getRecords()
  } catch (error) {
    console.error('获取理赔详情失败:', error)
    ElMessage.error('获取理赔详情失败')
  } finally {
    loading.value = false
  }
}

// 获取各种记录
const getRecords = async () => {
  const id = route.query.id as string
  if (!id) return

  try {
    recordsLoading.value = true

    // 获取沟通记录
    try {
      communicationRecords.value = await OrderClaimApi.getClaimRecordsListByOrderClaimId(Number(id))
    } catch (error) {
      console.error('获取沟通记录失败:', error)
      communicationRecords.value = []
    }

    // 获取理赔定责记录
    try {
      const liabilityData = await OrderClaimApi.getClaimLiableListByOrderClaimId(Number(id))
      liabilityRecords.value = liabilityData.map(record => ({
        ...record,
        responsibleType: record.personLiableType,
        liabilityType: record.liableType,
        penaltyAmount: record.punishAmount,
        liabilityAmount: record.liableAmount,
        responsibleName: record.userName
      }))
    } catch (error) {
      console.error('获取理赔定责记录失败:', error)
      liabilityRecords.value = []
    }

    // 构建审批记录（基于主记录的审批信息）
    if (formData.value.approvalId && formData.value.approvalName) {
      approvalRecords.value = [{
        approvalName: formData.value.approvalName,
        approvalTime: formData.value.approvalTime,
        remark: formData.value.remark || '',
        claimStatus: formData.value.claimStatus
      }]
    } else {
      approvalRecords.value = []
    }

  } catch (error) {
    console.error('获取记录失败:', error)
  } finally {
    recordsLoading.value = false
  }
}

// 复制理赔编号
const copyClaimId = () => {
  navigator.clipboard.writeText(formData.value.id?.toString() || '')
  ElMessage.success('理赔编号已复制到剪贴板')
}

// 跳转到运单详情
const handleWaybillNoClick = (waybillNo: string) => {
  router.push({ path: '/wbms/waybillDetailNew', query: { waybillNo: waybillNo } })
}

// 返回列表
const goBack = () => {
  router.back()
}

// 表单引用
const claimFormRef = ref()
const disposeFormRef = ref()

// 修改申请
const handleModifyApply = () => {
  claimFormRef.value?.open('reviseApply', formData.value.id)
}

// 理赔撤销
const handleRevoke = async () => {
  try {
    await ElMessageBox.confirm('确认撤销该理赔申请吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const updateData = { ...formData.value, claimStatus: 1 }
    await OrderClaimApi.updateOrderClaim(updateData)
    ElMessage.success('理赔已撤销')
    await getClaimDetail()
  } catch (error) {
    console.error('撤销理赔失败:', error)
  }
}

// 理赔审批
const handleApproval = () => {
  claimFormRef.value?.open('approval', formData.value.id)
}

// 理赔处理
const handleProcess = () => {
  router.push({ path: '/oems/orderclaim/process', query: { id: formData.value.id } })
}

// 理赔付款
const handlePayment = () => {
  router.push({ path: '/oems/orderclaim/process', query: { id: formData.value.id, mode: 'payment' } })
}

// 表单操作成功后的处理
const handleFormSuccess = () => {
  getClaimDetail()
  getRecords()
}

// 页面初始化
onMounted(() => {
  getClaimDetail()
})
</script>

<style scoped>
.header-bar {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 0;
}
.header-bar .order-no {
  color: #f25643;
  margin-right: 16px;
  font-size: 14px;
}
.header-bar .title {
  font-size: 20px;
  text-align-last: justify;
  color: #303133;
}
.header-bar .time {
  color: #909399;
  font-size: 14px;
}
.form-value {
  color: #303133;
  font-size: 14px;
}
.tab {
  padding: 8px 16px;
  margin-right: 24px;
  color: #606266;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s;
}
.tab.active {
  color: #409eff;
  border-bottom-color: #409eff;
}
.tab-bar {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  background: #fff;
  width: 100%;
}
.tab-list {
  display: flex;
  align-items: center;
  flex: 1;
}



.custom-steps-bar {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
  min-width: 300px;
}
.custom-steps-flex {
  display: flex;
  align-items: center;
}
.custom-step-flex {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
}
.custom-step-label {
  color: #909399;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 4px;
  text-align: center;
  line-height: 1.2;
  padding: 0 8px;
}
.custom-step-label.active {
  color: #13c08a;
}
.custom-step-icon {
  font-size: 16px;
  color: #c0c4cc;
  margin-top: 4px;
}
.custom-step-icon.active {
  color: #13c08a;
}
.custom-step-line-flex {
  width: 50px;
  height: 3px;
  background: #ebeef5;
  margin: 0 4px 20px 0;
  flex: none;
}

.custom-step-line-flex.active {
  background: #13c08a;
    height: 1.5px;
}

.section-title-row {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}
.section-title {
  font-size: 16px;
  margin-bottom: 0;
  font-weight: normal;
}
.section-right {
  color: #909399;
  font-size: 14px;
  margin-left: 16px;
}
.table-section {
  margin: 8px 0 12px 0;
}
.table-section {
  margin: 8px 0 12px 0;
}
.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 0;
  align-items: flex-start;
  flex-wrap: wrap;
}
.block-form {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 14px;
  margin-bottom: 16px;
  background: #fff;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
}
.block-form .el-form-item {
  min-width: 220px;
  margin-bottom: 8px;
}
.block-form .el-form-item:last-of-type {
  margin-bottom: 0 !important;
}
.block-form .section-title,
.block-form .section-title-row {
  margin-bottom: 8px;
}
.button-group-form {
  margin-bottom: 16px;
  background: transparent;
  display: flex;
  justify-content: flex-end;
}
.button-group-right {
  display: flex;
  gap: 12px;
}
.action-btn {
  border-radius: 6px;
  background: #fff;
  border: 1px solid #dcdfe6;
  color: #303133;
  font-weight: 400;
  min-width: 60px;
  padding: 0 18px;
}
.action-btn.el-button--primary {
  background: #409eff;
  color: #fff;
  border: 1px solid #409eff;
}
.action-btn.el-button--success {
  background: #67c23a;
  color: #fff;
  border: 1px solid #67c23a;
}
.action-btn.el-button--danger {
  background: #f56c6c;
  color: #fff;
  border: 1px solid #f56c6c;
}
.action-btn.el-button--warning {
  background: #e6a23c;
  color: #fff;
  border: 1px solid #e6a23c;
}

</style>
