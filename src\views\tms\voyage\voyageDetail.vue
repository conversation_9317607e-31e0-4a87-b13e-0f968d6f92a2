<template>
  <!-- 顶部栏，紧贴卡片 -->
  <div class="header-bar">
    <div class="order-no">
      <span>车次编号：{{ formData.voyageNo }}</span>
      <el-icon style="margin-left: 8px;color:#606266;cursor:pointer" @click="copyVoyageNo"><CopyDocument /></el-icon>
    </div>
    <div class="title">运 输 车 次 详 情</div>
    <div class="time">车次日期：{{ formatToDateTime(formData.voyageTime) }}</div>
  </div>
  <el-card style="border: none;box-shadow: none">
    <!-- tab栏 -->
    <div class="tab-bar">
      <div class="tab-list">
        <div
          v-for="(tab, i) in tabList"
          :key="tab.text" style="cursor: pointer"
          :class="['tab', {active: i === activeTabIndex}]"
          @click="activeTabIndex = i"
        >
          {{tab.text}}
        </div>
      </div>
      <div class="custom-steps-bar">
        <div class="custom-steps-flex">
          <template v-for="(step, i) in stepList" :key="step.text">
            <div class="custom-step-flex">
              <div :class="['custom-step-label', { active: step.done }]">{{ step.text }}</div>
              <el-icon :class="['custom-step-icon', {active: step.done}]">
                <CircleCheck v-if="step.done" />
              </el-icon>
            </div>
            <div
              v-if="i < stepList.length - 1"
              :class="['custom-step-line-flex', { active: stepList[i + 1].done }]"
            ></div>
          </template>
        </div>
      </div>
    </div>

    <!-- 基础信息 Tab -->
    <div v-show="activeTabIndex === 0" class="tab-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        v-loading="formLoading"
        label-suffix=":"
      >
        <!-- 车次信息 -->
        <div class="block-form">
          <div class="section-title-row">
            <div class="section-title">基础信息</div>
          </div>
          <el-form-item label="运输任务编号"><span class="form-value">{{ formData.transportNo || '无' }}</span></el-form-item>
          <el-form-item label="车次合同号"><span class="form-value">{{ formData.contractNum || '无' }}</span></el-form-item>
          <el-form-item label="封签号"><span class="form-value">{{ formData.closeLabelno || '无' }}</span></el-form-item>
          <el-form-item label="预计到达时间"><span class="form-value">{{ formData.expectarriveTime ? dateFormatter(formData.expectarriveTime) : '无' }}</span></el-form-item>
          <el-form-item v-if="formData.lateReason" label="晚点原因"><span class="form-value">{{ formData.lateReason }}</span></el-form-item>
          <el-form-item label="车牌号"><span class="form-value">{{ formData.vehicleLicenseNumber || '无' }}</span></el-form-item>
          <el-form-item label="车辆类型">
            <dict-tag :type="DICT_TYPE.VEHICLE_TYPE" :value="formData.vehicleType" v-if="formData.vehicleType" />
            <span class="form-value" v-else>无</span>
          </el-form-item>
          <el-form-item label="车次类型">
            <dict-tag :type="DICT_TYPE.TRANSPORT_TYPE" :value="formData.voyageType" v-if="formData.voyageType" />
            <span class="form-value" v-else>无</span>
          </el-form-item>
          <el-form-item label="车次状态">
            <dict-tag :type="DICT_TYPE.VOYAGE_STATUS" :value="formData.voyageStatus" v-if="formData.voyageStatus" />
            <span class="form-value" v-else>无</span>
          </el-form-item>
          <el-form-item label="车次日期"><span class="form-value">{{ formatToDateTime(formData.voyageTime)}}</span></el-form-item>
          <el-row style="width: 100%;">
          <el-form-item label="备注"><span class="form-value">{{ formData.remark || '无' }}</span></el-form-item>
          </el-row>
        </div>

        <!-- 驾驶员信息 -->
        <div class="block-form">
          <div class="section-title-row">
            <div class="section-title">驾驶员信息</div>
          </div>
          <el-form-item label="驾驶员1"><span class="form-value">{{ formData.driverName || '无' }}</span></el-form-item>
          <el-form-item label="驾驶员1手机号"><span class="form-value">{{ formData.contactMobile || '无' }}</span></el-form-item>
          <el-form-item label="驾驶员2" v-if="formData.driverName2"><span class="form-value">{{ formData.driverName2 }}</span></el-form-item>
          <el-form-item label="驾驶员2手机号" v-if="formData.contactMobile2"><span class="form-value">{{ formData.contactMobile2 }}</span></el-form-item>
        </div>

        <!-- 运输信息 -->
        <div class="block-form">
          <div class="section-title-row">
            <div class="section-title">运输信息</div>
          </div>
          <el-form-item label="发车日期" v-if="formData.departureTime"><span class="form-value">{{ formatToDateTime(formData.departureTime) }}</span></el-form-item>
          <el-form-item label="签收到达日期" v-if="formData.arrivedTime"><span class="form-value">{{ formatToDateTime(formData.arrivedTime) }}</span></el-form-item>
          <el-form-item label="车次里程" v-if="formData.voyageMileage"><span class="form-value">{{ formData.voyageMileage }}公里</span></el-form-item>
          <el-form-item label="发车部门"><span class="form-value">{{ formData.loadBranchname || '无' }}</span></el-form-item>
          <el-form-item label="到达部门"><span class="form-value">{{ formData.discBranchname || '无' }}</span></el-form-item>
          <el-form-item label="线路名称"><span class="form-value">{{ formData.transLinename || '无' }}</span></el-form-item>
        </div>

        <!-- 操作按钮 -->
        <div class="block-form button-group-right">
          <div class="button-group-right">
            <!-- 发车部门按钮 -->
            <template v-if="isLoadDept">
              <el-button
                v-if="Number(formData.voyageStatus) === 1 || Number(formData.voyageStatus) === 2"
                type="primary"
                class="action-btn"
                @click="handleCargoLoading"
                v-hasPermi="['tms:voyage:update']"
              >
                货物配载
              </el-button>
              <el-button
                v-if="Number(formData.voyageStatus) === 1 || Number(formData.voyageStatus) === 2"
                type="primary"
                class="action-btn"
                @click="openForm('update', true)"
                v-hasPermi="['tms:voyage:update']"
              >
                编辑/发车
              </el-button>
              <el-button
                v-if="Number(formData.voyageStatus) === 1"
                type="danger"
                class="action-btn"
                @click="handleCancelVoyage"
                v-hasPermi="['tms:voyage:delete']"
              >
                取消车次
              </el-button>
            </template>

            <!-- 到达部门按钮 -->
            <template v-if="isDiscDept">
              <el-button
                v-if="Number(formData.voyageStatus) === 3"
                type="primary"
                class="action-btn"
                @click="handleArrivalConfirm"
                v-hasPermi="['tms:reach-voyage:update']"
              >
                到车确认
              </el-button>
              <el-button
                v-if="Number(formData.voyageStatus) === 4 || Number(formData.voyageStatus) === 5"
                type="primary"
                class="action-btn"
                @click="handleDeliverySign"
                v-hasPermi="['tms:reach-voyage:update']"
              >
                到货签收
              </el-button>
            </template>
          </div>
        </div>
      </el-form>
    </div>

    <!-- 已配载订单 Tab -->
    <div v-show="activeTabIndex === 1" class="tab-content">
      <el-form class="dialogCardRight">
        <template #header>
          <span>已配载订单</span>
        </template>
        <el-table
          :data="selectedData"
          style="width: 100%"
          ref="rightTable"
          :header-cell-style="{ color: '#606266', textAlign: 'center', fontWeight: 400 }"
          :cell-style="{ textAlign: 'center' }"
        >
          <el-table-column type="selection" width="10" />
          <el-table-column fixed label="运单号" align="center" prop="waybillNo" min-width="140">
            <template #default="scope">
              <el-link @click="handleWaybillNoClick(scope.row.waybillNo)">
                {{ scope.row.waybillNo }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="运达部门" align="center" prop="position.endDeptName" min-width="100"/>
          <el-table-column label="目的地" align="center" prop="position.destinationName" />
          <el-table-column label="发货人" align="center" prop="shipper.name" min-width="100"/>
          <el-table-column label="收货人" align="center" prop="consignee.name" min-width="100"/>
          <el-table-column label="开票时间" prop="createTime" :formatter="dateFormatter" min-width="160"/>
          <el-table-column label="件数" align="center" prop="goodsList[0].goodsNum" />
          <el-table-column label="重量" align="center" prop="goodsList[0].goodsWeight" />
          <el-table-column label="体积" align="center" prop="goodsList[0].goodsVolume" />
          <el-table-column label="现付" align="center" prop="fee.xianfuHj" />
          <el-table-column label="提付" align="center" prop="fee.tifuHj" />
          <el-table-column label="回单付" align="center" prop="fee.huidanfuHj" />
          <el-table-column label="月结" align="center" prop="fee.xianfuyuejieHj" />
          <el-table-column label="佣金" align="center" prop="fee.yongjin" />
          <el-table-column label="送货费" align="center" prop="fee.songhuofei" />
          <el-table-column label="中转费" align="center" prop="fee.waizhuanfei" />
          <el-table-column label="代收款" align="center" prop="fee.collectPayments" />
          <el-table-column label="净运费" align="center" prop="fee.jingyunfei" />
        </el-table>
      </el-form>
    </div>
  </el-card>

  <!-- 表单弹窗：货物配载 -->
  <WaybillAssemble ref="formWaybill" v-model:visible="cargoDialogVisible" title="货物配载" />
  <!-- 表单弹窗：到货签收 -->
  <ReachWaybillAssemble ref="formReachWaybill" v-model:visible="deliveryDialogVisible" title="到货签收" />
  <!-- 表单弹窗：编辑/发车 -->
  <VoyageForm ref="editForm" @closed="getVoyageData" @success="getVoyageData" />
</template>
<script setup>
import { ref, onMounted, reactive, watch, computed } from 'vue'
import { getStrDictOptions, getIntDictOptions, DICT_TYPE, getDictLabel } from '@/utils/dict'
import { VoyageApi } from '@/api/tms/voyage'
import DriverSelector from '@/components/DriverSelector/DriverSelector.vue'
import VehicleSelector from '@/components/VehicleSelector/VehicleSelector.vue'
import { TransportApi } from '@/api/tms/transport'
import { ElMessage } from 'element-plus'
import { WaybillApi } from '@/api/wbms/waybill'
import * as DeptApi from '@/api/system/dept'
import { useRouter, useRoute } from 'vue-router'
import { formatToDateTime } from '@/utils/dateUtil'
import { dateFormatter } from '@/utils/formatTime'
import { CopyDocument, CircleCheck } from '@element-plus/icons-vue'
import { EventRouteApi } from '@/api/wbms/eventroute'
import { getUserProfile } from '@/api/system/user/profile'
import { getLoginUserTenant } from '@/api/system/tenant'
import VoyageForm from './VoyageForm.vue'
import WaybillAssemble from './WaybillAssemble.vue'
import ReachWaybillAssemble from '../reach/voyage/WaybillAssemble.vue'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const router = useRouter()
const route = useRoute()

const detail = ref()

const branchShow = ref(false)
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('update') // 表单的类型：create - 新增；update - 修改

const formRules = reactive({})

// Tab列表
const tabList = [
  { text: '基础信息'},
  { text: '已配载订单'}
]
const activeTabIndex = ref(0)

// 车次状态步骤
const statusMap = {
  plan: [1], // 车次计划
  loading: [2], // 货物配载
  shipping: [3], // 车次在途
  arrived: [4], // 车次到车
  completed: [5] // 卸货完成
}

const getCurrentStep = (status) => {
  if (statusMap.plan.includes(status)) return 0
  if (statusMap.loading.includes(status)) return 1
  if (statusMap.shipping.includes(status)) return 2
  if (statusMap.arrived.includes(status)) return 3
  if (statusMap.completed.includes(status)) return 4
  return -1 // 未命中任何状态
}

const stepLabels = ['车次计划', '货物配载', '车次在途', '车次到车', '卸货完成']
const stepList = computed(() => {
  const status = Number(formData.value.voyageStatus)
  const currentStep = getCurrentStep(status)
  // 如果currentStep为-1，所有都不亮
  return stepLabels.map((text, idx) => ({
    text,
    done: currentStep >= 0 && idx <= currentStep
  }))
})

// 判断是否为发车部门
const isLoadDept = computed(() => {
  return loginDept.value && formData.value.loadBranchid === loginDept.value.id
})

// 判断是否为到达部门
const isDiscDept = computed(() => {
  return loginDept.value && formData.value.discBranchid === loginDept.value.id
})

const formRef = ref() // 表单 Ref

const formData = ref({
  id: undefined,
  voyageNo: undefined,
  voyageTime: undefined,
  voyageType: undefined,
  voyageMileage: undefined,
  arrivedTime: undefined,
  transportId: undefined,
  transportNo: undefined,
  departureTime: undefined,
  closeLabelno: undefined,
  voyageStatus: undefined,
  loadBranchid: undefined,
  loadBranchname: undefined,
  discBranchid: undefined,
  discBranchname: undefined,
  transLinename: undefined,
  vehicleId: undefined,
  vehicleLicenseNumber: undefined,
  vehicleType: undefined,
  driverId: undefined,
  driverName: undefined,
  contactMobile: undefined,
  driverId2: undefined,
  driverName2: undefined,
  contactMobile2: undefined,
  contractNum: undefined,
  sealStatus: undefined,
  expectarriveTime: undefined,
  lateReason: undefined,
  remark: undefined
})

const options = ref([])

const loginDept = ref()

const voyageNo = ref()
const dialogVisible = ref()
const multipleSelection = ref([])
const loading = ref(true) // 列表的加载中
const dataSource = ref([]) // 数据源
const selectedData = ref([]) // 已选项的数据
const waybillNos = ref([]) // 存储运单号的数组
const voyage = ref()
const total1 = ref(0) // 列表的总页数
const total2 = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  waybillNo: undefined,
  createTime: undefined,
  startDeptId: undefined,
  endDeptId: undefined,
  waybillStatus: '1'
})

const queryFormRef = ref() // 搜索的表单

const isLeftButtonDisabled = ref(false) // 新定义的响应式变量
const isRightButtonDisabled = ref(false) // 新定义的响应式变量

// 新增变量
const loginUserProfile = ref()
const loginTenant = ref()
const formWaybill = ref() // 货物配载组件
const formReachWaybill = ref() // 到货签收组件
const cargoDialogVisible = ref(false) // 货物配载弹窗
const deliveryDialogVisible = ref(false) // 到货签收弹窗

onMounted(async () => {
  try {
    // 获取登录部门信息
    loginDept.value = await DeptApi.getLoginUserDept()

    // 获取用户信息和租户信息
    loginUserProfile.value = await getUserProfile()
    loginTenant.value = await getLoginUserTenant()

    // 获取车次编号
    voyageNo.value = route.query.voyageNo || route.params.voyageNo

    console.log('路由参数:', route.query)
    console.log('车次编号:', voyageNo.value)

    if (voyageNo.value) {
      // 获取车次数据
      voyage.value = await VoyageApi.getVoyageByVoyageNo(voyageNo.value)
      formData.value = { ...voyage.value }

      console.log('车次数据:', formData.value)
      console.log('登录部门:', loginDept.value)
      console.log('发车部门ID:', formData.value.loadBranchid)
      console.log('到达部门ID:', formData.value.discBranchid)

      // 重置列表数据
      dataSource.value = []
      selectedData.value = []
      waybillNos.value = []
      queryParams.waybillNo = undefined
      queryParams.createTime = undefined
      queryParams.endDeptId = voyage.value?.discBranchid
      queryParams.startDeptId = loginDept.value?.id

      // 获取最新数据
      getList()
      getVoyageOrderList()
    } else {
      message.error('缺少车次编号参数')
    }
  } catch (error) {
    console.error('初始化失败:', error)
    message.error('页面初始化失败')
  }
})

// const getVoyage = () =>{
//     formLoading.value = true
//     try {
//       formData.value = await VoyageApi.getVoyage(id)
//       console.log(formData.value)
//     } finally {
//       formLoading.value = false
//     }
// }

// /** 提交表单 */
// const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

const submitForm = async () => {
  //await VoyageApi.getVoyageOrderListByVoyageId(formData.value.id)
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  // try {
  //   await voyageOrderFormRef.value.validate()
  // } catch (e) {
  //   subTabsName.value = 'voyageOrder'
  //   return
  // }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value

    if (formType.value === 'create') {
      await VoyageApi.createVoyage(data)
      message.success(t('common.createSuccess'))
    } else {
      await VoyageApi.updateVoyage(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const startVoyage = async () => {
  if (formData.value.transportId != undefined) {
    const VoyageData = formData.value

    const data = await VoyageApi.getVoyageOrderListByVoyageId(formData.value.id)
    const orderbarNosArray = data.orderbarNos.split('+')
    const waybills = await WaybillApi.selectWaybills(orderbarNosArray)
    const waybillList = waybills.list
    // 更新选中的行
    for (const waybill of waybillList) {
      waybill.waybillStatus = '3'
      await WaybillApi.updateWaybillStatus(waybill)
    }

    VoyageData.voyageStatus = 3
    VoyageData.sealStatus = 1

    const transport = await TransportApi.getTransport(formData.value.transportId)

    transport.transportStatus = 2

    TransportApi.updateTransport(transport)

    await VoyageApi.updateVoyage(VoyageData)
    message.success(t('common.updateSuccess'))
    dialogVisible.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    transportId: undefined,
    transportNo: undefined,
    departureTime: undefined,
    closeLabelno: undefined,
    loadBranchid: undefined,
    loadBranchname: undefined,
    discBranchid: undefined,
    discBranchname: undefined,
    vehicleId: undefined,
    vehicleLicenseNumber: undefined,
    vehicleType: undefined,
    driverId: undefined,
    driverName: undefined,
    contactMobile: undefined,
    driverId2: undefined,
    driverName2: undefined,
    contactMobile2: undefined,
    contractNum: undefined,
    expectarriveTime: undefined,
    lateReason: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}

const handleBranchSelected1 = (branchInfo) => {
  formData.value.loadBranchname = branchInfo.branchName
  //   transportSelect()
}

const handleBranchSelected2 = (branchInfo) => {
  formData.value.discBranchname = branchInfo.branchName
  transportSelect()
}

const handleDriverSelected1 = (driverInfo) => {
  formData.value.driverName = driverInfo.driverName
  formData.value.contactMobile = driverInfo.contactMobile
}

const handleDriverSelected2 = (driverInfo) => {
  formData.value.driverName2 = driverInfo.driverName
  formData.value.contactMobile2 = driverInfo.contactMobile
}

const handleVehicleSelected = (vehicleInfo) => {
  formData.value.vehicleLicenseNumber = vehicleInfo.vehicleLicenseNumber
  formData.value.vehicleType = vehicleInfo.vehicleType
}



const transportSelect = async () => {
  formData.value.transportId = undefined
  formData.value.transportNo = undefined
  formData.value.contractNum = undefined

  try {
    const response = await TransportApi.getTransportList(
      formData.value.loadBranchid,
      formData.value.discBranchid
    )

    const data = response || []
    options.value = data
    console.log(options.value)
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败，请稍后再试！')
  }
}
const handleTransportChange = (value) => {
  const selectedTransport = options.value.find((item) => item.transportNo === value)
  formData.value.contractNum = selectedTransport.contractNum
  formData.value.transportId = selectedTransport.transportId
  formData.value.transportNo = selectedTransport.transportNo
  formData.value.vehicleId = selectedTransport.vehicleId
  formData.value.vehicleLicenseNumber = selectedTransport.vehicleLicenseNumber
  formData.value.vehicleType = selectedTransport.vehicleType
  formData.value.driverId = selectedTransport.driverId
  formData.value.driverName = selectedTransport.driverName
  formData.value.contactMobile = selectedTransport.contactMobile
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await WaybillApi.getWaybillPage(queryParams)
    dataSource.value = data.list
    total1.value = data.total
  } finally {
    loading.value = false
  }
}

/** 查询回显列表 */
const getVoyageOrderList = async () => {
  const data = await VoyageApi.getVoyageOrderListByVoyageId(voyage.value?.id)

  //将返回的字符串从 '+' 号分割成数组
  const orderbarNosArray = data.orderbarNos.split('+')

  if (orderbarNosArray.length !== 0) {
    loading.value = true
    try {
      const data = await WaybillApi.selectWaybills(orderbarNosArray)
      selectedData.value = data.list
    } finally {
      loading.value = false
    }
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 处理左侧表格的选择变化 */
const leftTableSelectionChange = (selection) => {
  multipleSelection.value = selection

  // 更新 isButtonDisabled 的值
  isLeftButtonDisabled.value = multipleSelection.value.length > 0
}

/** 右侧表格的选择变化 */
const rightTableSelectionChange = (selection) => {
  multipleSelection.value = selection
  isRightButtonDisabled.value = multipleSelection.value.length > 0
}

/** 将数据从左侧移动到右侧 */
const giveDataToAlreadyTable = () => {
  const selectedRows = multipleSelection.value

  // 更新选中的行
  for (const row of selectedRows) {
    row.waybillStatus = '2'
    WaybillApi.updateWaybillStatus(row)
  }

  selectedData.value.push(...selectedRows)
  selectedData.value = selectedData.value.sort((a, b) => b.id - a.id) // 按 ID 降序排序
  // WaybillApi.updateWaybill(queryParams)
  // 从 dataSource 中移除已选中的行
  dataSource.value = dataSource.value.filter((row) => !selectedRows.includes(row))

  selectedRows.forEach((row) => {
    waybillNos.value.push(row.waybillNo)
  })

  multipleSelection.value = []
}

/** 将数据从右侧移动到左侧 */
const giveDataToBeTable = () => {
  const selectedRows = multipleSelection.value

  // 更新选中的行
  for (const row of selectedRows) {
    row.waybillStatus = '1'
    WaybillApi.updateWaybillStatus(row)
  }

  dataSource.value.push(...selectedRows)
  dataSource.value = dataSource.value.sort((a, b) => b.id - a.id)

  // 从 dataSource 中移除已选中的行
  selectedData.value = selectedData.value.filter((row) => !selectedRows.includes(row))

  // 更新运单号数组
  waybillNos.value = waybillNos.value.filter(
    (wn) => !selectedRows.some((row) => row.waybillNo === wn)
  )

  multipleSelection.value = []
}

/** 装配完成按钮点击事件 */
const handleComplete = async () => {
  const waybillNosStr = waybillNos.value.join('+')
  if (waybillNosStr == '') {
    voyage.value.voyageStatus = 1
    VoyageApi.updateVoyage(voyage.value)
  } else {
    voyage.value.voyageStatus = 2
    VoyageApi.updateVoyage(voyage.value)
  }
  try {
    const result = await VoyageApi.createVoyageOrder(voyage.value?.id, waybillNosStr)
    console.log('Result:', result)
    emit('success') // 向父组件发送成功事件
    dialogVisible.value = false // 关闭对话框
  } catch (error) {
    console.error('Error creating voyage order:', error)
  }
}

/** 设置表格行的类名 */
const tableRowClassNameMySelf = (row, rowIndex) => {
  if (rowIndex % 2 === 0) {
    return 'even-row'
  }
  return ''
}

const handleWaybillNoClick = (waybillNo) => {
    router.push({ path: '/wbms/waybillDetailNew', query: { waybillNo: waybillNo } })
    // 你可以在这里添加更多的逻辑
}
const editForm = ref()
const openForm = (type, branch) => {
  if (formData.value.voyageStatus == 1 || formData.value.voyageStatus == 2) {
    editForm.value.open(type, branch, loginDept.value, loginTenant.value.businessType, formData.value.id)
  } else {
    message.error(t('请选择未发车车次！'))
  }
}

defineExpose({ open })

/** 初始化 */
onMounted(() => {
  getList()
})

// 定义 props
const props = defineProps({
  visible: Boolean,
  title: String
})

// 定义 emits
const emit = defineEmits(['update:visible', 'success'])

watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal

    if (newVal) {
      // 重置所有数据
      dataSource.value = []
      selectedData.value = []
      waybillNos.value = []
      queryParams.waybillNo = undefined
      queryParams.createTime = undefined
      queryParams.endDeptId = undefined
      // queryParams.startDeptId = undefined

      // 获取最新数据
      getList()
    }
  }
)

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

const goToBack = () => {
  router.go(-1)
}

// 复制车次编号
const copyVoyageNo = () => {
  console.log("点击复制")
  const text = formData.value.voyageNo
  if (navigator.clipboard && navigator.clipboard.writeText) {
    navigator.clipboard.writeText(text).then(() => {
      message.success('车次编号已复制')
    }).catch(() => {
      message.error('复制失败，请手动复制')
    })
  } else {
    // 兼容老浏览器
    const input = document.createElement('input')
    input.value = text
    document.body.appendChild(input)
    input.select()
    try {
      document.execCommand('copy')
      message.success('车次编号已复制')
    } catch (e) {
      message.error('复制失败，请手动复制')
    }
    document.body.removeChild(input)
  }
}

// 货物配载
const handleCargoLoading = () => {
  if (formData.value.voyageStatus == 1 || formData.value.voyageStatus == 2) {
    cargoDialogVisible.value = true
    if (loginTenant.value?.businessType === 3) {
      formWaybill.value.open(formData.value, loginDept.value.id, formData.value.voyageType)
    } else {
      formWaybill.value.open(formData.value, loginDept.value.id)
    }
  } else {
    message.error(t('请选择未发车车次！'))
  }
}

// 编辑/发车
const handleEditDeparture = () => {
  if (formData.value.voyageStatus == 1 || formData.value.voyageStatus == 2) {
    formRef.value.open('update', true, loginDept.value, loginTenant.value?.businessType, formData.value.id)
  } else {
    message.error(t('请选择未发车车次！'))
  }
}

// 取消车次
const handleCancelVoyage = async () => {
  if (formData.value.voyageStatus == 1) {
    try {
      // 删除的二次确认
      await message.delConfirm()
      // 发起删除
      await VoyageApi.deleteVoyage(formData.value.id)
      message.success(t('common.delSuccess'))
      // 返回列表页
      goToBack()
    } catch {}
  } else {
    message.error(t('只有车次计划状态的车次可以取消发车!'))
  }
}

// 到车确认
const handleArrivalConfirm = async () => {
  if (formData.value.voyageStatus == 3) {
    formData.value.voyageStatus = 4
    await VoyageApi.updateVoyageStatus(formData.value)

    const data = await VoyageApi.getVoyageOrderListByVoyageId(formData.value.id)
    const orderbarNosArray = data.orderbarNos.split('+')

    // 更新运单状态
    for (const waybill of orderbarNosArray) {
      const waybillInfo = await WaybillApi.getWaybillByWaybillNo(waybill)
      if (waybillInfo.waybillStatus !== '6') {
        await handleSaveEventRoute(waybill, formData.value)
      }
    }

    message.success(t('common.updateSuccess'))
    // 刷新页面数据
    await getVoyageData()
  } else {
    message.error(t('请选择在途车次！'))
  }
}

// 到货签收
const handleDeliverySign = () => {
  if (formData.value.voyageStatus == 1 || formData.value.voyageStatus == 2) {
    message.error(t('该车次未发车！'))
  } else {
    deliveryDialogVisible.value = true
    formReachWaybill.value.open(formData.value)
  }
}

// 保存事件路由
const handleSaveEventRoute = async (waybillNo, voyage) => {
  const eventroute = {
    deptId: loginUserProfile.value?.dept.id,
    deptName: loginUserProfile.value?.dept.name,
    eventNo: waybillNo,
    eventLog: `[${voyage.loadBranchname ?? ''}]发往[${voyage.discBranchname ?? ''}]，车牌号[${voyage.vehicleLicenseNumber ?? ''}]驾驶员[${voyage.driverName ?? ''}]随车手机[${voyage.contactMobile ?? ''}],已抵达`,
    eventNode: '16111',
    eventNodeValue: '车次到车',
    eventType: '16201',
    userCode: loginUserProfile.value?.id,
    userName: loginUserProfile.value?.username
  }

  await EventRouteApi.createEventRoute(eventroute)
}

// 获取车次数据
const getVoyageData = async () => {
  voyage.value = await VoyageApi.getVoyageByVoyageNo(voyageNo.value)
  formData.value = voyage.value
}


</script>

<style scoped>
/* 头部栏样式 */
.header-bar {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}
.header-bar .order-no {
  color: #f25643;
  margin-right: 16px;
  font-size: 14px;
  display: flex;
  align-items: center;
}
.header-bar .title {
  font-size: 20px;
  text-align-last: justify;
}
.header-bar .time {
  color: #909399;
  font-size: 14px;
}

/* Tab栏样式 */
.tab-bar {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  background: #fff;
  width: 100%;
}
.tab-list {
  display: flex;
  align-items: center;
  flex: 1;
}
.tab {
  position: relative;
  padding: 0 0 8px 0;
  display: flex;
  align-items: center;
  color: #606266;
  font-size: 15px;
  margin-right: 20px;
}
.tab.active {
  color: #409eff;
  font-weight: 500;
}
.tab.active::after {
  content: '';
  display: block;
  position: absolute;
  left: 50%;
  bottom: -2px;
  transform: translateX(-50%);
  width: 62px;
  height: 2px;
  background: #409eff;
  border-radius: 1px;
}

/* 状态步骤条样式 */
.custom-steps-bar {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
  min-width: 400px;
}
.custom-steps-flex {
  display: flex;
  align-items: center;
}
.custom-step-flex {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
}
.custom-step-label {
  color: #909399;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 4px;
  text-align: center;
  line-height: 1.2;
  padding: 0 8px;
}
.custom-step-label.active {
  color: #13c08a;
}
.custom-step-icon {
  font-size: 16px;
  color: #c0c4cc;
  margin-top: 4px;
}
.custom-step-icon.active {
  color: #13c08a;
}
.custom-step-line-flex {
  width: 50px;
  height: 3px;
  background: #ebeef5;
  margin: 0 4px 20px 0;
  flex: none;
}
.custom-step-line-flex.active {
  background: #13c08a;
    height: 1.5px;
}

/* 表单块样式 */
.block-form {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 14px;
  margin-bottom: 16px;
  background: #fff;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
}
.block-form .el-form-item {
  min-width: 220px;
  margin-bottom: 8px;
}
.block-form .el-form-item:last-of-type {
  margin-bottom: 0 !important;
}

/* 表单值样式 */
.form-value {
  color: #303133;
  font-size: 14px;
}

.section-title-row {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}
.section-title {
  font-size: 16px;
  margin-bottom: 0;
  font-weight: normal;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 0;
  align-items: flex-start;
  flex-wrap: wrap;
}



/* 表格样式 */
.even-row {
  background-color: #f2f2f2;
}

.OperationPositionButtonFather {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.OperationPositionButton {
  margin: 10px;
}

/* 按钮组样式 */
.button-group-form {
  margin-top: 20px;
  padding: 16px;
  border-top: 1px solid #ebeef5;
}

.button-group-right {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.action-btn {
  min-width: 80px;
}
</style>
