<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="网点" prop="logisticsNetworkId">
        <el-select
          v-model="queryParams.logisticsNetworkId"
          placeholder="请输入网点名称或账号"
          clearable
          filterable
          remote remote-show-suffix
          :remote-method="handleRemoteNetwork"
          class="!w-240px"
        >
          <el-option
            v-for="item in networkList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="物流公司" prop="logisticsCompaniesId">
        <el-select
          v-model="queryParams.logisticsCompaniesId" placeholder="请选择物流公司" clearable filterable class="!w-240px"
        >
          <el-option v-for="item in companyList" :key="item.id" :label="item.name" :value="item.id"/>
        </el-select>
      </el-form-item>
      <el-form-item label="订单号" prop="orderCode">
        <el-input
          v-model="queryParams.orderCode"
          placeholder="请输入订单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="运单号" prop="waybillCode">
        <el-input
          v-model="queryParams.waybillCode"
          placeholder="请输入运单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="结算状态" prop="networkCalcStatus">
        <el-select
          v-model="queryParams.networkCalcStatus"
          placeholder="请选择"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in SETTLE_STATUS_LIST"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开单时间" prop="waybillCreateTime">
        <el-date-picker
          v-model="queryParams.waybillCreateTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="签收时间" prop="signTime">
        <el-date-picker
          v-model="queryParams.signTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="结算时间" prop="networkCalcTime">
        <el-date-picker
          v-model="queryParams.networkCalcTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
<!--        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['finance:network-calculate-log:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>-->
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['finance:network-calculate-log:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
<!--        <el-button-->
<!--          type="primary" plain @click="handleReach" :loading="reachLoading"-->
<!--          v-hasPermi="['finance:network-calculate-log:settle']"-->
<!--        ><Icon icon="ep:refresh" class="mr-5px" /> 同步</el-button>-->
        <el-button
          type="primary" plain @click="handleSettle" :loading="settleLoading"
          v-hasPermi="['finance:network-calculate-log:settle']"
        ><Icon icon="fa-solid:donate" class="mr-5px" /> 结算</el-button>
        <el-button
          type="primary" plain @click="handleAudit" :loading="auditLoading"
          v-hasPermi="['finance:network-calculate-log:audit']"
        ><Icon icon="fa-solid:check-circle" class="mr-5px" /> 审核</el-button>
        <el-button
          type="primary" plain @click="handleArchive" :loading="archiveLoading"
          v-hasPermi="['finance:network-calculate-log:archive']"
        ><Icon icon="fa-solid:file-archive" class="mr-5px" /> 归档</el-button>
        <el-button
          type="primary" plain @click="handleTransfer" :loading="transferLoading"
          v-hasPermi="['finance:network-calculate-log:transfer']"
        ><Icon icon="fa-solid:exchange-alt" class="mr-5px" /> 划转</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" ref="listRef" :data="list" :stripe="true" :show-overflow-tooltip="true" show-summary :summary-method="getSummaries">
<!--      <el-table-column label="自增序列" align="center" prop="id" />-->
      <el-table-column type="selection" :selectable="selectable" width="57" />
<!--      <el-table-column label="订单号" width="170px" fixed align="center" prop="orderCode" >-->
<!--        <template #default="scope">-->
<!--          <router-link :to="'/financeadmin/orderChild/orderDetail/' + scope.row.orderCode">-->
<!--            <el-button link type="primary">{{ scope.row.orderCode }}</el-button>-->
<!--          </router-link>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="运单号" width="170px" align="center" prop="waybillNo" >
        <template #default="scope">
          <el-link @click="handleWaybillNoClick(scope.row.waybillNo)">
            {{ scope.row.waybillNo }}
          </el-link>
        </template>
      </el-table-column>
<!--      <el-table-column label="网点id" align="center" prop="logisticsNetworkId" />-->
      <el-table-column label="开单网点" align="center" prop="logisticsNetworkName" min-width="100px"/>
<!--      <el-table-column label="物流公司" align="center" prop="logisticsCompaniesName" />-->
      <el-table-column label="网点运费预付账户" align="center" prop="networkFreightPreValue" min-width="140px">
        <template #default="scope">
          <span v-if="scope.row.networkFreightPreValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.networkFreightPreValue, 2) }}</span>
          <span v-if="scope.row.networkFreightPreValue < 0" style="color: crimson">{{ formatMoney(scope.row.networkFreightPreValue, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="网点到付授信额度" align="center" prop="networkFreightPreOverAllowValue" min-width="140px">
        <template #default="scope">
          <span v-if="scope.row.networkFreightPreOverAllowValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.networkFreightPreOverAllowValue, 2) }}</span>
          <span v-if="scope.row.networkFreightPreOverAllowValue < 0" style="color: crimson">{{ formatMoney(scope.row.networkFreightPreOverAllowValue, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="网点运费分成账户" align="center" prop="networkFreightDivideValue" min-width="140px">
        <template #default="scope">
          <span v-if="scope.row.networkFreightDivideValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.networkFreightDivideValue, 2) }}</span>
          <span v-if="scope.row.networkFreightDivideValue < 0" style="color: crimson">{{ formatMoney(scope.row.networkFreightDivideValue, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="网点税费账户" align="center" prop="networkTaxValue" min-width="120px">
        <template #default="scope">
          <span v-if="scope.row.networkTaxValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.networkTaxValue, 2) }}</span>
          <span v-if="scope.row.networkTaxValue < 0" style="color: crimson">{{ formatMoney(scope.row.networkTaxValue, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="网点佣金账户" align="center" prop="networkBrokerageValue" min-width="120px">
        <template #default="scope">
          <span v-if="scope.row.networkBrokerageValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.networkBrokerageValue, 2) }}</span>
          <span v-if="scope.row.networkBrokerageValue < 0" style="color: crimson">{{ formatMoney(scope.row.networkBrokerageValue, 2) }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="平台运费账户" align="center" prop="platformFreightValue" >
        <template #default="scope">
          <span v-if="scope.row.platformFreightValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.platformFreightValue, 2) }}</span>
          <span v-if="scope.row.platformFreightValue < 0" style="color: crimson">{{ formatMoney(scope.row.platformFreightValue, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="平台毛利账户" align="center" prop="platformProfitValue" >
        <template #default="scope">
          <span v-if="scope.row.platformProfitValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.platformProfitValue, 2) }}</span>
          <span v-if="scope.row.platformProfitValue < 0" style="color: crimson">{{ formatMoney(scope.row.platformProfitValue, 2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="平台佣金账户" align="center" prop="platformBrokerageValue" >
        <template #default="scope">
          <span v-if="scope.row.platformBrokerageValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.platformBrokerageValue, 2) }}</span>
          <span v-if="scope.row.platformBrokerageValue < 0" style="color: crimson">{{ formatMoney(scope.row.platformBrokerageValue, 2) }}</span>
        </template>
      </el-table-column>-->
<!--      <el-table-column
        label="清分时间"
        align="center"
        prop="calcTime"
        :formatter="dateFormatter"
        width="180px"
      />-->
      <el-table-column label="开单时间" align="center" prop="createTime" :formatter="dateFormatter" width="180px"/>
      <el-table-column label="签收时间" align="center" prop="signTime" :formatter="dateFormatter" width="180px"/>
      <el-table-column label="结算归档时间" align="center" prop="networkCalcTime" :formatter="dateFormatter" width="180px"/>
      <el-table-column label="收货人姓名" width="100px" align="center" prop="collectName" />
      <el-table-column label="收货人手机号" width="130px" align="center" prop="collectPhone" />
      <el-table-column label="收货人地址" width="130px" align="center" prop="collectAddress" />
      <el-table-column label="寄件人姓名" width="100px" align="center" prop="sendName" />
      <el-table-column label="寄件人手机号" width="130px" align="center" prop="sendPhone" />
<!--      <el-table-column label="寄件人身份证号" align="center" prop="sendIdCard" />-->
      <el-table-column label="寄件人地址" width="130px" align="center" prop="sendAddress" />
<!--      <el-table-column label="物品类型（数据字典）" align="center" prop="goodsType" />-->
      <el-table-column label="货品名称" align="center" prop="goodsTypeLabel" />
      <el-table-column label="总重量（Kg）" width="130px" align="center" prop="totalWeight" />
      <el-table-column label="总体积（m³）" width="130px" align="center" prop="totalVolume" />
      <el-table-column label="总件数（件）" width="130px" align="center" prop="totalNum" />
<!--      <el-table-column label="物品长（cm）" width="130px" align="center" prop="goodsLong" />-->
<!--      <el-table-column label="物品宽（cm）" width="130px" align="center" prop="goodsWidth" />-->
<!--      <el-table-column label="物品高（cm）" width="130px" align="center" prop="goodsHeight" />-->
      <el-table-column label="付款方式" width="100px" align="center" prop="payMethod" >
        <template #default="scope">
          <span type="danger">{{ getDictLabel(DICT_TYPE.SETTLEMENT_TYPE, scope.row.payMethod) }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="应付金额" align="center" prop="shouldPayAmount" />-->
<!--      <el-table-column label="是否回单：0 否 1 是" align="center" prop="isReceipt" />-->
<!--      <el-table-column label="回单类型（1-电子回单 2-纸质回单）" align="center" prop="receiptType" />-->
<!--      <el-table-column label="送货方式：1 自提 2 送货" align="center" prop="deliveryMethod" />-->
<!--      <el-table-column label="放货方式：1 等通知 2 其他" align="center" prop="releaseMethod" />-->
<!--      <el-table-column label="包装方式：1 箱 2 纸 3 袋 4 桶 5 木 6 膜 7 皮 8 布" align="center" prop="packMethod" />-->
      <el-table-column label="代收货款" align="center" prop="collectionDelivery" />
      <el-table-column label="代收手续费" align="center" prop="collectionPayAmount" min-width="120px"/>
<!--      <el-table-column label="代收款人" align="center" prop="collectionName" />-->
<!--      <el-table-column label="收款行" align="center" prop="collectionBankName" />-->
<!--      <el-table-column label="收款卡号" align="center" prop="collectionCardNum" />-->
      <el-table-column label="保价" align="center" prop="insuredAmount" />
      <el-table-column label="保价费" align="center" prop="insuredPayAmount" />
      <el-table-column label="控货费" align="center" prop="releaseAmount" />
      <el-table-column label="送货费" align="center" prop="deliveryAmount" />
      <el-table-column label="制单费" align="center" prop="amountZdf" />
      <el-table-column label="回单费" align="center" prop="receiptAmount" />
      <el-table-column label="运费" align="center" prop="amountFreight" />
      <el-table-column label="佣金" align="center" prop="brokerageAmount" />
      <el-table-column label="税费" align="center" prop="taxAmount" />
      <el-table-column label="其他费用" align="center" prop="otherAmount" />
      <el-table-column label="代收运费" align="center" prop="collectionFreightAmount" />
      <el-table-column label="优惠金额" align="center" prop="discountAmount" />
      <el-table-column label="总金额" align="center" prop="totalAmount" />
      <el-table-column label="实付金额" align="center" prop="actualPayAmount" />
<!--      <el-table-column label="佣金返款方式（1-现返 2-欠返）" align="center" prop="brokerageBackType" />-->
<!--      <el-table-column label="保值费率(‰)" align="center" prop="rateBzf" />-->
<!--      <el-table-column label="代收款手续费率(‰)" align="center" prop="collectionProcRate" />-->
<!--      <el-table-column label="备注" align="center" prop="remark" />-->
      <el-table-column label="结算状态" align="center" fixed="right" prop="networkCalcStatus" >
        <template #default="scope">
          <span>{{ getSettleStatusLabel(scope.row.networkCalcStatus) }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作" align="center" min-width="60px" fixed="right">
        <template #default="scope">
          <el-button
            v-if="['0', '2'].indexOf(scope.row.networkCalcStatus) > -1"
            link type="primary" @click="openForm('update', scope.row.id)"
            v-hasPermi="['finance:network-calculate-log:update']"
          >编辑</el-button>
        </template>
      </el-table-column> -->
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <NetworkSettleChangeForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { NetworkCalculateLogApi, NetworkCalculateLogVO } from '@/api/finance/networkcalculatelog'
import NetworkSettleChangeForm from './NetworkSettleChangeForm.vue'
//import {LoginInfoOuterApi} from "@/api/finance/logininfoouter";
import { BranchApi } from "@/api/system/branch";
//import { TakeCashApplyApi } from "@/api/finance/takecashapply";
import {getSettleStatusLabel, SETTLE_STATUS_LIST} from "@/api/finance/accountinfo";
import {DICT_TYPE, getDictLabel} from "@/utils/dict";
import {formatMoney} from "@/utils/formatter";
//import {LogisticsCompaniesInfoApi} from "@/api/finance/logisticscompaniesinfo";
import type { TableColumnCtx } from 'element-plus'
//import { WaybillApi } from "@/api/wbms/waybill";
import { useRouter } from 'vue-router'

/** 网点结算 列表 */
defineOptions({ name: 'NetworkCalculateSettle' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter()
const loading = ref(true) // 列表的加载中
const list = ref<NetworkCalculateLogVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  roleType: 'network',
  waybillType: undefined,
  logisticsNetworkId: undefined,
  logisticsCompaniesId: undefined,
  orderCode: undefined,
  waybillCode: undefined,
  networkCalcStatus: undefined,
  waybillCreateTime: [],
  signTime: [],
  networkCalcTime: [],
  companyCalcTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const listRef = ref() // 表格

// 行是否可被选中
const selectable = (row) => row.networkCalcStatus != '6'

/** 后台查询网点 */
const networkList = ref([])
const handleRemoteNetwork = async (param: string) => {
  if (param) {
    networkList.value = await BranchApi.getBranchList({
      branchName: param
    })
  } else {
    networkList.value = []
  }
}

/** 获取物流公司下拉框数据 */
const companyList = ref([])
// const getCompaniesListApi = async () => {
//   companyList.value = await LogisticsCompaniesInfoApi.getCompaniesSelectAll()
// }

interface SummaryMethodProps<T = NetworkCalculateLogVO> {
  columns: TableColumnCtx<T>[]
  data: T[]
}
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param
  const sums: (string | VNode)[] = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = h('div', { style: { } }, [
        '合计',
      ])
      return
    }
    const values = data.map((item) => Number(item[column.property]))
    if (['networkFreightPreValue', 'networkFreightPreOverAllowValue',
      'networkFreightDivideValue', 'networkTaxValue', 'networkBrokerageValue',
      'platformFreightValue', 'platformProfitValue', 'platformBrokerageValue'
    ].indexOf(column.property) > -1) {
      sums[index] = `${formatMoney(values.reduce((prev, curr) => {
        const value = Number(curr)
        if (!Number.isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0), 2)}`
    } else {
      sums[index] = ''
    }
  })

  return sums
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await NetworkCalculateLogApi.getNetworkSettlePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openFnpmorm = (type: string, id?: number) => {
  console.log('openForm', type, id)
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await NetworkCalculateLogApi.deleteNetworkCalculateLog(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await NetworkCalculateLogApi.exportNetworkSettle(queryParams)
    download.excel(data, '网点结算.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 获取批量操作请求参数 */
const getRequestData = () => {
  let checkList = listRef.value.getSelectionRows()
  if (checkList.length > 0) {
    return  {
      pageNo: 1,
      pageSize: 10,
      roleType: queryParams.roleType,
      waybillCodeList: checkList.map(item => item.waybillNo)
    }
  } else {
    return {...queryParams}
  }
}

/** 同步 按钮操作 */
// const reachLoading = ref(false) // 同步的加载中
// const handleReach = async () => {
//   try {
//     await WaybillApi.reachWaybillInfoAll()
//     await getList()
//     reachLoading.value = true
//   } catch {
//   } finally {
//     reachLoading.value = false
//   }
// }
/** 结算 按钮操作 */
const settleLoading = ref(false) // 结算的加载中
const handleSettle = async () => {
  try {
    // 二次确认
    await message.confirm('确认进行对当前数据进行结算操作吗？')
    settleLoading.value = true
    await NetworkCalculateLogApi.settle(getRequestData())
    await getList()
  } catch {
  } finally {
    settleLoading.value = false
  }
}

/** 审核 按钮操作 */
const auditLoading = ref(false) // 审核的加载中
const handleAudit = async () => {
  try {
    // 二次确认
    await message.confirm('确认进行对当前数据进行审核操作吗？')
    auditLoading.value = true
    await NetworkCalculateLogApi.audit(getRequestData())
    await getList()
  } catch {
  } finally {
    auditLoading.value = false
  }
}

/** 归档 按钮操作 */
const archiveLoading = ref(false) // 归档的加载中
const handleArchive = async () => {
  try {
    // 二次确认
    await message.confirm('确认进行对当前数据进行归档操作吗？')
    archiveLoading.value = true
    await NetworkCalculateLogApi.archive(getRequestData())
    await getList()
  } catch {
  } finally {
    archiveLoading.value = false
  }
}

/** 划转 按钮操作 */
const transferLoading = ref(false) // 划转的加载中
const handleTransfer = async () => {
  try {
    // 二次确认
    await message.confirm('确认进行对当前数据进行划转操作吗？')
    transferLoading.value = true
    await NetworkCalculateLogApi.transfer(getRequestData())
    await getList()
  } catch {
  } finally {
    transferLoading.value = false
  }
}

const handleWaybillNoClick = (waybillNo: string) => {

  router.push({ path: '/wbms/waybillDetailNew', query: { waybillNo: waybillNo } })
  // 你可以在这里添加更多的逻辑
}


/** 初始化 **/
onMounted(() => {
  getList()
  // getCompaniesListApi()
})
</script>
