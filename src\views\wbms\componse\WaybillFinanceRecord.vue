<template>
  <!-- 财务记录 -->
  <el-form class="block-form" inline label-suffix=":">
      <el-table
        :data="financeList"
        style="width: 100%;"
        :header-cell-style="{ color: '#606266', textAlign: 'center', fontWeight: 400 }"
        :cell-style="{ textAlign: 'center' }"
      >
        <el-table-column prop="operateTime" label="操作时间" min-width="160" :formatter="dateFormatter" />
        <el-table-column prop="networkPlatformName" label="网点/平台" min-width="120" />
        <el-table-column prop="operateUser" label="操作人" min-width="100" />
        <el-table-column prop="operateType" label="操作类型" min-width="100" />
        <el-table-column prop="accountName" label="账户" min-width="120" />
        <el-table-column prop="amount" label="金额" min-width="120">
          <template #default="scope">
            <span v-if="scope.row.amount >= 0" style="color: #08b926">+{{ formatMoney(scope.row.amount, 2) }}</span>
            <span v-if="scope.row.amount < 0" style="color: crimson">{{ formatMoney(scope.row.amount, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="financeRecordTime" label="财务记录时间" min-width="160" :formatter="dateFormatter" />
      </el-table>
  </el-form>

</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { dateFormatter } from '@/utils/formatTime'
import { formatMoney } from '@/utils/formatter'

// 定义props
const props = defineProps<{
  waybillNo: string
  financeList?: any[]
}>()

// 财务记录相关
const financeList = ref<any[]>([])

// 监听props变化，更新列表数据
watch(() => props.financeList, (newData) => {
  financeList.value = newData || []
}, { immediate: true })
</script>

<style scoped>
.section-title {
  font-size: 16px;
  margin-bottom: 8px;
  font-weight: normal;
}

.table-section {
  margin: 8px 0 12px 0;
}

.block-form {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 14px;
  margin-bottom: 16px;
  background: #fff;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
}
</style>
