<template>
  <!-- 结算记录 -->
  <el-form class="block-form" inline label-suffix=":">
    <div class="section-title" style="width: 100%;">结算记录</div>
    <div class="table-section" style="width: 100%;">
      <el-table
        v-loading="calculateLoading"
        :data="calculateList"
        border
        style="width: 100%;"
        :header-cell-style="{ color: '#606266', textAlign: 'center', fontWeight: 400 }"
        :cell-style="{ textAlign: 'center' }"
      >
        <el-table-column prop="logisticsNetworkName" label="起始网点名称" min-width="120" />
        <el-table-column prop="networkFreightPreValue" label="起始网点运费预付账户增加金额" min-width="240">
          <template #default="scope">
            <span v-if="scope.row.networkFreightPreValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.networkFreightPreValue, 2) }}</span>
            <span v-if="scope.row.networkFreightPreValue < 0" style="color: crimson">{{ formatMoney(scope.row.networkFreightPreValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="networkFreightPreOverAllowValue" label="起始网点到付授信余额增加金额" min-width="240">
          <template #default="scope">
            <span v-if="scope.row.networkFreightPreOverAllowValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.networkFreightPreOverAllowValue, 2) }}</span>
            <span v-if="scope.row.networkFreightPreOverAllowValue < 0" style="color: crimson">{{ formatMoney(scope.row.networkFreightPreOverAllowValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="networkFreightDivideValue" label="起始网点运费分成账户增加金额" min-width="240">
          <template #default="scope">
            <span v-if="scope.row.networkFreightDivideValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.networkFreightDivideValue, 2) }}</span>
            <span v-if="scope.row.networkFreightDivideValue < 0" style="color: crimson">{{ formatMoney(scope.row.networkFreightDivideValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="networkTaxValue" label="起始网点税费账户增加金额" min-width="240">
          <template #default="scope">
            <span v-if="scope.row.networkTaxValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.networkTaxValue, 2) }}</span>
            <span v-if="scope.row.networkTaxValue < 0" style="color: crimson">{{ formatMoney(scope.row.networkTaxValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="networkBrokerageValue" label="起始网点佣金账户增加金额" min-width="240">
          <template #default="scope">
            <span v-if="scope.row.networkBrokerageValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.networkBrokerageValue, 2) }}</span>
            <span v-if="scope.row.networkBrokerageValue < 0" style="color: crimson">{{ formatMoney(scope.row.networkBrokerageValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="platformFreightValue" label="平台运费账户增加金额" min-width="200">
          <template #default="scope">
            <span v-if="scope.row.platformFreightValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.platformFreightValue, 2) }}</span>
            <span v-if="scope.row.platformFreightValue < 0" style="color: crimson">{{ formatMoney(scope.row.platformFreightValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="platformProfitValue" label="平台毛利账户增加金额" min-width="200">
          <template #default="scope">
            <span v-if="scope.row.platformProfitValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.platformProfitValue, 2) }}</span>
            <span v-if="scope.row.platformProfitValue < 0" style="color: crimson">{{ formatMoney(scope.row.platformProfitValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="platformBrokerageValue" label="平台佣金账户增加金额" min-width="200">
          <template #default="scope">
            <span v-if="scope.row.platformBrokerageValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.platformBrokerageValue, 2) }}</span>
            <span v-if="scope.row.platformBrokerageValue < 0" style="color: crimson">{{ formatMoney(scope.row.platformBrokerageValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="discNetworkName" label="到达网点名称" min-width="120" />
        <el-table-column prop="discNetworkFreightPreValue" label="到达网点运费预付账户增加金额" min-width="240">
          <template #default="scope">
            <span v-if="scope.row.discNetworkFreightPreValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.discNetworkFreightPreValue, 2) }}</span>
            <span v-if="scope.row.discNetworkFreightPreValue < 0" style="color: crimson">{{ formatMoney(scope.row.discNetworkFreightPreValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="discNetworkFreightDivideValue" label="到达网点运费分成账户增加金额" min-width="240">
          <template #default="scope">
            <span v-if="scope.row.discNetworkFreightDivideValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.discNetworkFreightDivideValue, 2) }}</span>
            <span v-if="scope.row.discNetworkFreightDivideValue < 0" style="color: crimson">{{ formatMoney(scope.row.discNetworkFreightDivideValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="calcTime" label="结算时间" min-width="160" :formatter="dateFormatter" />
      </el-table>
    </div>
  </el-form>

  <!-- 划转记录 -->
  <el-form class="block-form" inline label-suffix=":">
    <div class="section-title" style="width: 100%;">划转记录</div>
    <div class="table-section" style="width: 100%;">
      <el-table
        v-loading="transferLoading"
        :data="transferList"
        border
        style="width: 100%;"
        :header-cell-style="{ color: '#606266', textAlign: 'center', fontWeight: 400 }"
        :cell-style="{ textAlign: 'center' }"
      >
        <el-table-column prop="logisticsNetworkId" label="起始网点id" min-width="120" />
        <el-table-column prop="networkFreightPreValue" label="起始网点运费预付账户增加金额" min-width="240">
          <template #default="scope">
            <span v-if="scope.row.networkFreightPreValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.networkFreightPreValue, 2) }}</span>
            <span v-if="scope.row.networkFreightPreValue < 0" style="color: crimson">{{ formatMoney(scope.row.networkFreightPreValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="networkFreightPreOverAllowValue" label="起始网点到付授信余额增加金额" min-width="240">
          <template #default="scope">
            <span v-if="scope.row.networkFreightPreOverAllowValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.networkFreightPreOverAllowValue, 2) }}</span>
            <span v-if="scope.row.networkFreightPreOverAllowValue < 0" style="color: crimson">{{ formatMoney(scope.row.networkFreightPreOverAllowValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="networkFreightDivideValue" label="起始网点运费分成账户增加金额" min-width="240">
          <template #default="scope">
            <span v-if="scope.row.networkFreightDivideValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.networkFreightDivideValue, 2) }}</span>
            <span v-if="scope.row.networkFreightDivideValue < 0" style="color: crimson">{{ formatMoney(scope.row.networkFreightDivideValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="networkTaxValue" label="起始网点税费账户增加金额" min-width="240">
          <template #default="scope">
            <span v-if="scope.row.networkTaxValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.networkTaxValue, 2) }}</span>
            <span v-if="scope.row.networkTaxValue < 0" style="color: crimson">{{ formatMoney(scope.row.networkTaxValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="networkBrokerageValue" label="起始网点佣金账户增加金额" min-width="200">
          <template #default="scope">
            <span v-if="scope.row.networkBrokerageValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.networkBrokerageValue, 2) }}</span>
            <span v-if="scope.row.networkBrokerageValue < 0" style="color: crimson">{{ formatMoney(scope.row.networkBrokerageValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="platformFreightValue" label="平台运费账户增加金额" min-width="200">
          <template #default="scope">
            <span v-if="scope.row.platformFreightValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.platformFreightValue, 2) }}</span>
            <span v-if="scope.row.platformFreightValue < 0" style="color: crimson">{{ formatMoney(scope.row.platformFreightValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="platformProfitValue" label="平台毛利账户增加金额" min-width="160">
          <template #default="scope">
            <span v-if="scope.row.platformProfitValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.platformProfitValue, 2) }}</span>
            <span v-if="scope.row.platformProfitValue < 0" style="color: crimson">{{ formatMoney(scope.row.platformProfitValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="platformBrokerageValue" label="平台佣金账户增加金额" min-width="160">
          <template #default="scope">
            <span v-if="scope.row.platformBrokerageValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.platformBrokerageValue, 2) }}</span>
            <span v-if="scope.row.platformBrokerageValue < 0" style="color: crimson">{{ formatMoney(scope.row.platformBrokerageValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="discNetworkName" label="到达网点名称" min-width="120" />
        <el-table-column prop="discNetworkFreightPreValue" label="到达网点运费预付账户增加金额" min-width="180">
          <template #default="scope">
            <span v-if="scope.row.discNetworkFreightPreValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.discNetworkFreightPreValue, 2) }}</span>
            <span v-if="scope.row.discNetworkFreightPreValue < 0" style="color: crimson">{{ formatMoney(scope.row.discNetworkFreightPreValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="discNetworkFreightDivideValue" label="到达网点运费分成账户增加金额" min-width="180">
          <template #default="scope">
            <span v-if="scope.row.discNetworkFreightDivideValue >= 0" style="color: #08b926">+{{ formatMoney(scope.row.discNetworkFreightDivideValue, 2) }}</span>
            <span v-if="scope.row.discNetworkFreightDivideValue < 0" style="color: crimson">{{ formatMoney(scope.row.discNetworkFreightDivideValue, 2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="transferTime" label="划转时间" min-width="160" :formatter="dateFormatter" />
      </el-table>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { NetworkCalculateLogApi, NetworkCalculateLogVO } from '@/api/finance/networkcalculatelog'
import { NetworkTransferLogApi, NetworkTransferLogVO } from '@/api/finance/networktransferlog'
import { dateFormatter } from '@/utils/formatTime'
import { formatMoney } from '@/utils/formatter'

// 定义props
const props = defineProps<{
  waybillNo: string
}>()

// 结算记录相关
const calculateLoading = ref(false)
const calculateList = ref<NetworkCalculateLogVO[]>([])

// 划转记录相关
const transferLoading = ref(false)
const transferList = ref<NetworkTransferLogVO[]>([])

// 获取结算记录
const getCalculateList = async () => {
  if (!props.waybillNo) return

  calculateLoading.value = true
  try {
    const params = {

      waybillNo: props.waybillNo
    }
    const data = await NetworkCalculateLogApi.getNetworkCalculateLogPage(params)
    calculateList.value = data.list || []
  } catch (error) {
    console.error('获取结算记录失败:', error)
  } finally {
    calculateLoading.value = false
  }
}

// 获取划转记录
const getTransferList = async () => {
  if (!props.waybillNo) return

  transferLoading.value = true
  try {
    const params = {
      waybillCodeSet: props.waybillNo
    }
    const data = await NetworkTransferLogApi.getNetworkTransferLogPage(params)
    transferList.value = data.list || []
  } catch (error) {
    console.error('获取划转记录失败:', error)
  } finally {
    transferLoading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  getCalculateList()
  getTransferList()
})
</script>

<style scoped>
.section-title {
  font-size: 16px;
  margin-bottom: 8px;
  font-weight: normal;
}

.table-section {
  margin: 8px 0 12px 0;
}

.block-form {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 14px;
  margin-bottom: 16px;
  background: #fff;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
}
</style>
